"""
Stock Universe Manager - S&P 500 + $100B+ Market Cap Stocks
"""
import requests
import logging
from typing import List, Dict, Any, Set
from config import API_CONFIG


class StockUniverse:
    """Manages the universe of stocks to scan"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.sp500_stocks: List[str] = []
        self.large_cap_stocks: List[str] = []
        self.all_stocks: Set[str] = set()
        
    def get_sp500_stocks(self) -> List[str]:
        """Get all S&P 500 stocks from FMP API"""
        try:
            url = f"https://financialmodelingprep.com/api/v3/sp500_constituent"
            params = {"apikey": API_CONFIG.fmp_api_key}
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            sp500_symbols = []
            for stock in data:
                symbol = stock.get('symbol', '').strip()
                if symbol and len(symbol) <= 5:  # Filter out weird symbols
                    sp500_symbols.append(symbol)
            
            self.logger.info(f"Retrieved {len(sp500_symbols)} S&P 500 stocks")
            self.sp500_stocks = sp500_symbols
            return sp500_symbols
            
        except Exception as e:
            self.logger.error(f"Error fetching S&P 500 stocks: {e}")
            # Fallback to major S&P 500 stocks
            return self._get_fallback_sp500()
    
    def get_large_cap_stocks(self, min_market_cap: float = 100_000_000_000) -> List[str]:
        """Get stocks with market cap >= $100B"""
        try:
            # Get market cap data for major stocks
            url = f"https://financialmodelingprep.com/api/v3/market-capitalization"
            params = {
                "apikey": API_CONFIG.fmp_api_key,
                "limit": 1000  # Get top 1000 by market cap
            }
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            large_cap_symbols = []
            for stock in data:
                market_cap = stock.get('marketCap', 0)
                symbol = stock.get('symbol', '').strip()
                
                if (market_cap >= min_market_cap and 
                    symbol and 
                    len(symbol) <= 5 and
                    '.' not in symbol):  # Filter out weird symbols
                    large_cap_symbols.append(symbol)
            
            self.logger.info(f"Retrieved {len(large_cap_symbols)} stocks with market cap >= ${min_market_cap/1e9:.0f}B")
            self.large_cap_stocks = large_cap_symbols
            return large_cap_symbols
            
        except Exception as e:
            self.logger.error(f"Error fetching large cap stocks: {e}")
            # Fallback to known large cap stocks
            return self._get_fallback_large_cap()
    
    def _get_fallback_sp500(self) -> List[str]:
        """Fallback S&P 500 list if API fails"""
        return [
            # Technology
            "AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "NVDA", "META", "TSLA", "AVGO", "ORCL",
            "CRM", "ADBE", "NFLX", "AMD", "INTC", "CSCO", "ACN", "IBM", "QCOM", "TXN",
            
            # Healthcare
            "UNH", "JNJ", "PFE", "ABBV", "TMO", "ABT", "DHR", "MRK", "BMY", "AMGN",
            "MDT", "GILD", "CVS", "CI", "ANTM", "HUM", "BIIB", "REGN", "VRTX", "ISRG",
            
            # Financial
            "BRK.B", "JPM", "BAC", "WFC", "GS", "MS", "C", "AXP", "USB", "TFC",
            "PNC", "COF", "SCHW", "CB", "MMC", "AON", "SPGI", "ICE", "CME", "MCO",
            
            # Consumer Discretionary
            "AMZN", "TSLA", "HD", "MCD", "NKE", "SBUX", "LOW", "TJX", "BKNG", "MAR",
            "GM", "F", "APTV", "YUM", "CMG", "ORLY", "AZO", "ULTA", "RCL", "CCL",
            
            # Consumer Staples
            "PG", "KO", "PEP", "WMT", "COST", "MDLZ", "CL", "KMB", "GIS", "K",
            "HSY", "MKC", "SJM", "CPB", "CAG", "TSN", "HRL", "CLX", "CHD", "EL",
            
            # Energy
            "XOM", "CVX", "COP", "EOG", "SLB", "PSX", "VLO", "MPC", "OXY", "BKR",
            "HAL", "DVN", "FANG", "APA", "EQT", "CTRA", "MRO", "HES", "KMI", "OKE",
            
            # Industrials
            "BA", "CAT", "HON", "UPS", "RTX", "LMT", "GE", "MMM", "FDX", "NOC",
            "UNP", "CSX", "NSC", "LUV", "DAL", "AAL", "UAL", "WM", "RSG", "EMR",
            
            # Materials
            "LIN", "APD", "SHW", "FCX", "NEM", "DOW", "DD", "PPG", "ECL", "IFF",
            "ALB", "CE", "VMC", "MLM", "NUE", "STLD", "PKG", "IP", "WRK", "AVY",
            
            # Real Estate
            "AMT", "PLD", "CCI", "EQIX", "PSA", "EXR", "AVB", "EQR", "DLR", "SBAC",
            "WY", "BXP", "ARE", "VTR", "PEAK", "ESS", "MAA", "UDR", "CPT", "FRT",
            
            # Utilities
            "NEE", "SO", "DUK", "AEP", "SRE", "D", "PEG", "EXC", "XEL", "WEC",
            "ED", "ETR", "ES", "FE", "EIX", "PPL", "CMS", "DTE", "NI", "LNT",
            
            # Communication Services
            "GOOGL", "META", "NFLX", "DIS", "CMCSA", "VZ", "T", "CHTR", "TMUS", "ATVI"
        ]
    
    def _get_fallback_large_cap(self) -> List[str]:
        """Fallback large cap stocks if API fails"""
        return [
            # Mega caps (>$500B)
            "AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "NVDA", "META", "TSLA", "BRK.B",
            
            # Large caps ($100B-$500B)
            "UNH", "JNJ", "AVGO", "XOM", "JPM", "PG", "HD", "CVX", "ABBV", "BAC",
            "ORCL", "KO", "PFE", "MRK", "COST", "TMO", "WMT", "ABT", "CRM", "MCD",
            "DHR", "VZ", "ADBE", "NFLX", "CMCSA", "PEP", "T", "LIN", "AMD", "TXN",
            "NEE", "PM", "RTX", "QCOM", "HON", "UPS", "LOW", "SPGI", "INTC", "INTU",
            "IBM", "GS", "CAT", "AMGN", "SBUX", "BKNG", "GILD", "AXP", "DE", "MDT",
            "BLK", "ELV", "SYK", "TJX", "LRCX", "SCHW", "ADP", "AMT", "CVS", "TMUS",
            "MDLZ", "C", "MO", "SO", "PLD", "REGN", "CB", "DUK", "ZTS", "MMC",
            "EOG", "CL", "ITW", "BSX", "FI", "WM", "SLB", "GD", "AON", "ICE",
            "APD", "CCI", "EQIX", "PGR", "EMR", "NSC", "SHW", "CME", "USB", "FCX",
            "PSA", "MCO", "DIS", "TFC", "GM", "MU", "KLAC", "ANET", "ORLY", "HCA"
        ]
    
    def build_universe(self) -> List[str]:
        """Build complete universe of stocks to scan"""
        self.logger.info("Building stock universe...")
        
        # Get S&P 500 stocks
        sp500 = self.get_sp500_stocks()
        self.all_stocks.update(sp500)
        
        # Get large cap stocks
        large_cap = self.get_large_cap_stocks()
        self.all_stocks.update(large_cap)
        
        # Convert to sorted list
        universe = sorted(list(self.all_stocks))
        
        self.logger.info(f"Built universe of {len(universe)} unique stocks")
        self.logger.info(f"  S&P 500: {len(sp500)} stocks")
        self.logger.info(f"  Large Cap ($100B+): {len(large_cap)} stocks")
        self.logger.info(f"  Total Unique: {len(universe)} stocks")
        
        return universe
    
    def get_sector_breakdown(self) -> Dict[str, List[str]]:
        """Get stocks organized by sector"""
        sectors = {
            "Technology": [
                "AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "NVDA", "META", "AVGO", "ORCL",
                "CRM", "ADBE", "NFLX", "AMD", "INTC", "CSCO", "ACN", "IBM", "QCOM", "TXN"
            ],
            "Healthcare": [
                "UNH", "JNJ", "PFE", "ABBV", "TMO", "ABT", "DHR", "MRK", "BMY", "AMGN",
                "MDT", "GILD", "CVS", "CI", "ANTM", "HUM", "BIIB", "REGN", "VRTX", "ISRG"
            ],
            "Financial": [
                "BRK.B", "JPM", "BAC", "WFC", "GS", "MS", "C", "AXP", "USB", "TFC",
                "PNC", "COF", "SCHW", "CB", "MMC", "AON", "SPGI", "ICE", "CME", "MCO"
            ],
            "Energy": [
                "XOM", "CVX", "COP", "EOG", "SLB", "PSX", "VLO", "MPC", "OXY", "BKR",
                "HAL", "DVN", "FANG", "APA", "EQT", "CTRA", "MRO", "HES", "KMI", "OKE"
            ],
            "Consumer": [
                "TSLA", "HD", "MCD", "NKE", "SBUX", "LOW", "TJX", "BKNG", "MAR", "PG",
                "KO", "PEP", "WMT", "COST", "MDLZ", "CL", "KMB", "GIS", "K", "HSY"
            ]
        }
        return sectors
    
    def get_high_volume_stocks(self) -> List[str]:
        """Get stocks with high trading volume for better liquidity"""
        return [
            # Mega volume stocks
            "SPY", "QQQ", "IWM", "AAPL", "MSFT", "NVDA", "TSLA", "AMZN", "META", "GOOGL",
            "AMD", "NFLX", "INTC", "BABA", "PLTR", "SOFI", "F", "BAC", "T", "PFE",
            "SQQQ", "TQQQ", "SOXL", "SOXS", "SPXL", "SPXS", "TNA", "TZA", "UPRO", "SPXU"
        ]


def create_scanning_batches(universe: List[str], batch_size: int = 50) -> List[List[str]]:
    """Split universe into batches for efficient scanning"""
    batches = []
    for i in range(0, len(universe), batch_size):
        batch = universe[i:i + batch_size]
        batches.append(batch)
    return batches


def main():
    """Test the stock universe builder"""
    print("=" * 60)
    print("STOCK UNIVERSE BUILDER TEST")
    print("=" * 60)
    
    universe_manager = StockUniverse()
    
    # Build the universe
    universe = universe_manager.build_universe()
    
    print(f"\nComplete Stock Universe ({len(universe)} stocks):")
    print("=" * 60)
    
    # Show first 50 stocks
    print("First 50 stocks:")
    for i, symbol in enumerate(universe[:50]):
        print(f"{symbol:6s}", end="")
        if (i + 1) % 10 == 0:
            print()  # New line every 10 stocks
    
    # Show sector breakdown
    sectors = universe_manager.get_sector_breakdown()
    print(f"\n\nSector Breakdown:")
    print("-" * 30)
    for sector, stocks in sectors.items():
        print(f"{sector}: {len(stocks)} stocks")
    
    # Show high volume stocks
    high_volume = universe_manager.get_high_volume_stocks()
    print(f"\nHigh Volume Stocks ({len(high_volume)}):")
    print("-" * 30)
    for i, symbol in enumerate(high_volume):
        print(f"{symbol:6s}", end="")
        if (i + 1) % 10 == 0:
            print()
    
    # Show batching
    batches = create_scanning_batches(universe, batch_size=50)
    print(f"\n\nScanning Batches:")
    print("-" * 30)
    print(f"Total stocks: {len(universe)}")
    print(f"Batch size: 50")
    print(f"Number of batches: {len(batches)}")
    
    print(f"\nFirst batch ({len(batches[0])} stocks):")
    print(", ".join(batches[0][:20]) + "...")


if __name__ == "__main__":
    main()
