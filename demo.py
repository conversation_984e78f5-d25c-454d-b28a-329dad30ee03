"""
Demo script to showcase the automated trading system capabilities.
"""
import time
from datetime import datetime, timedelta
import numpy as np
from automated_trading_system import AutomatedTradingSystem
from data_models import MarketData
from config import validate_config


def create_demo_data_with_pattern():
    """Create demo data that includes a momentum reversal pattern"""
    print("Creating demo market data with momentum reversal pattern...")
    
    base_time = datetime.now() - timedelta(hours=2)
    market_data = []
    base_price = 150.0
    
    # Phase 1: Normal trading (20 bars)
    for i in range(20):
        price_change = np.random.normal(0, 0.3)
        price = base_price + price_change
        
        data = MarketData(
            timestamp=base_time + timedelta(minutes=i),
            open=price,
            high=price + abs(np.random.normal(0, 0.2)),
            low=price - abs(np.random.normal(0, 0.2)),
            close=price + np.random.normal(0, 0.1),
            volume=int(np.random.normal(1500, 300))
        )
        market_data.append(data)
        base_price = data.close
    
    # Phase 2: Declining red momentum phase (6 bars) - This creates our pattern
    print("  Adding declining red momentum phase...")
    decline_rate = 0.8
    for i in range(6):
        # Progressively larger declines
        price_decline = decline_rate * (i + 1)
        price = base_price - price_decline
        
        data = MarketData(
            timestamp=base_time + timedelta(minutes=20 + i),
            open=base_price,
            high=base_price + 0.1,
            low=price - 0.2,
            close=price,
            volume=int(np.random.normal(2000, 400))  # Higher volume on decline
        )
        market_data.append(data)
        base_price = price
    
    # Phase 3: Momentum reversal (3 bars) - This triggers our signal
    print("  Adding momentum reversal...")
    for i in range(3):
        # Small recovery
        price_recovery = 0.3 * (i + 1)
        price = base_price + price_recovery
        
        data = MarketData(
            timestamp=base_time + timedelta(minutes=26 + i),
            open=base_price,
            high=price + 0.3,
            low=base_price - 0.1,
            close=price,
            volume=int(np.random.normal(1800, 350))
        )
        market_data.append(data)
        base_price = price
    
    # Phase 4: Continued upward movement (15 bars)
    print("  Adding continuation phase...")
    for i in range(15):
        price_change = np.random.normal(0.2, 0.4)  # Slight upward bias
        price = base_price + price_change
        
        data = MarketData(
            timestamp=base_time + timedelta(minutes=29 + i),
            open=base_price,
            high=price + abs(np.random.normal(0, 0.2)),
            low=min(base_price, price) - abs(np.random.normal(0, 0.1)),
            close=price,
            volume=int(np.random.normal(1400, 250))
        )
        market_data.append(data)
        base_price = price
    
    print(f"Created {len(market_data)} bars of demo data")
    return market_data


def run_demo():
    """Run the trading system demo"""
    print("=" * 60)
    print("AUTOMATED MOMENTUM REVERSAL TRADING SYSTEM DEMO")
    print("=" * 60)
    
    # Validate configuration
    if not validate_config():
        print("Configuration validation failed!")
        return
    
    try:
        # Initialize the system (this will use your API keys)
        print("\nInitializing trading system...")
        system = AutomatedTradingSystem()
        print("✓ Trading system initialized successfully")
        
        # Display system configuration
        print(f"\nSystem Configuration:")
        print(f"  Initial Capital: ${system.trading_engine.initial_capital:,.2f}")
        print(f"  Position Size: {system.trading_engine.position_size:.1%}")
        print(f"  Momentum Period: {system.technical_analyzer.momentum_indicator.period}")
        print(f"  ATR Period: {system.technical_analyzer.atr_trailing_stop.atr_period}")
        print(f"  Min Declining Bars: {system.pattern_detector.min_declining_bars}")
        
    except Exception as e:
        print(f"Error initializing system: {e}")
        print("This might be due to API connectivity. Continuing with offline demo...")
        return
    
    # Create demo data with pattern
    demo_data = create_demo_data_with_pattern()
    
    print(f"\nProcessing {len(demo_data)} bars of market data...")
    print("Looking for momentum reversal patterns...")
    
    # Process the demo data
    signals = []
    entry_count = 0
    exit_count = 0
    
    for i, data_point in enumerate(demo_data):
        # Process through the system
        signal = system.process_market_data("DEMO", data_point)
        
        if signal:
            signals.append(signal)
            
            # Track significant events
            if signal.pattern.entry_signal:
                entry_count += 1
                print(f"  Bar {i+1}: 🟢 ENTRY SIGNAL at ${data_point.close:.2f}")
                print(f"    - Declining Red Momentum: {signal.pattern.declining_red_momentum}")
                print(f"    - Momentum Reversal: {signal.pattern.momentum_reversal}")
                print(f"    - ATR Confirmation: {signal.pattern.atr_confirmation}")
            
            elif signal.pattern.exit_signal:
                exit_count += 1
                print(f"  Bar {i+1}: 🔴 EXIT SIGNAL at ${data_point.close:.2f}")
            
            elif signal.pattern.declining_red_momentum:
                print(f"  Bar {i+1}: 📉 Declining red momentum detected")
            
            elif signal.pattern.momentum_reversal:
                print(f"  Bar {i+1}: 🔄 Momentum reversal detected")
    
    # Display results
    print(f"\n" + "=" * 60)
    print("DEMO RESULTS")
    print("=" * 60)
    
    print(f"Total Bars Processed: {len(demo_data)}")
    print(f"Signals Generated: {len(signals)}")
    print(f"Entry Signals: {entry_count}")
    print(f"Exit Signals: {exit_count}")
    
    # Pattern statistics
    declining_red_count = sum(1 for s in signals if s.pattern.declining_red_momentum)
    reversal_count = sum(1 for s in signals if s.pattern.momentum_reversal)
    atr_confirm_count = sum(1 for s in signals if s.pattern.atr_confirmation)
    
    print(f"\nPattern Detection Statistics:")
    print(f"  Declining Red Momentum Phases: {declining_red_count}")
    print(f"  Momentum Reversals: {reversal_count}")
    print(f"  ATR Confirmations: {atr_confirm_count}")
    
    # Trading performance
    trades = system.trading_engine.trades
    if trades:
        total_pnl = sum(trade.pnl for trade in trades)
        winning_trades = sum(1 for trade in trades if trade.pnl > 0)
        win_rate = winning_trades / len(trades) if trades else 0
        
        print(f"\nTrading Performance:")
        print(f"  Total Trades Executed: {len(trades)}")
        print(f"  Winning Trades: {winning_trades}")
        print(f"  Win Rate: {win_rate:.1%}")
        print(f"  Total P&L: ${total_pnl:.2f}")
        print(f"  Final Capital: ${system.trading_engine.current_capital:.2f}")
        
        # Show individual trades
        print(f"\nTrade Details:")
        for i, trade in enumerate(trades, 1):
            duration_minutes = trade.duration_bars
            print(f"  Trade {i}: ${trade.entry_price:.2f} → ${trade.exit_price:.2f} "
                  f"(P&L: ${trade.pnl:.2f}, Duration: {duration_minutes} min)")
    else:
        print(f"\nNo trades were executed in this demo.")
        print("This could mean:")
        print("  - Pattern conditions were not fully met")
        print("  - ATR confirmation was not achieved")
        print("  - Market conditions didn't trigger entry signals")
    
    # Technical analysis summary
    if signals:
        momentum_values = [s.indicators.momentum for s in signals if s.indicators]
        atr_values = [s.indicators.atr_value for s in signals if s.indicators]
        
        if momentum_values and atr_values:
            print(f"\nTechnical Analysis Summary:")
            print(f"  Momentum Range: {min(momentum_values):.2f} to {max(momentum_values):.2f}")
            print(f"  Average Momentum: {sum(momentum_values)/len(momentum_values):.2f}")
            print(f"  ATR Range: {min(atr_values):.2f} to {max(atr_values):.2f}")
            print(f"  Average ATR: {sum(atr_values)/len(atr_values):.2f}")
    
    print(f"\n" + "=" * 60)
    print("DEMO COMPLETED")
    print("=" * 60)
    print("This demo showed how the system:")
    print("✓ Detects declining red momentum phases")
    print("✓ Identifies momentum reversal signals")
    print("✓ Validates with ATR confirmation")
    print("✓ Executes trades automatically")
    print("✓ Manages positions and risk")
    print("✓ Tracks performance metrics")
    
    print(f"\nTo run a real backtest, use:")
    print(f"  python main.py --mode backtest --symbol SPY")
    print(f"\nTo run live trading (paper), use:")
    print(f"  python main.py --mode live --symbols SPY QQQ")


def interactive_demo():
    """Run an interactive demo"""
    print("\n" + "=" * 60)
    print("INTERACTIVE DEMO MODE")
    print("=" * 60)
    
    while True:
        print("\nChoose an option:")
        print("1. Run pattern detection demo")
        print("2. Show system configuration")
        print("3. Test with different symbols")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            run_demo()
        elif choice == "2":
            show_configuration()
        elif choice == "3":
            test_symbols()
        elif choice == "4":
            print("Exiting demo. Thank you!")
            break
        else:
            print("Invalid choice. Please try again.")


def show_configuration():
    """Show current system configuration"""
    from config import TRADING_CONFIG, API_CONFIG, SYSTEM_CONFIG
    
    print("\nCurrent System Configuration:")
    print("-" * 40)
    print(f"Trading Settings:")
    print(f"  Initial Capital: ${TRADING_CONFIG.initial_capital:,.2f}")
    print(f"  Position Size: {TRADING_CONFIG.position_size_percent:.1%}")
    print(f"  Momentum Period: {TRADING_CONFIG.momentum_period}")
    print(f"  ATR Period: {TRADING_CONFIG.atr_period}")
    print(f"  Min Declining Bars: {TRADING_CONFIG.min_declining_bars}")
    
    print(f"\nAPI Settings:")
    print(f"  Alpaca Paper Trading: {API_CONFIG.alpaca_paper_trading}")
    print(f"  FMP API Configured: {'Yes' if API_CONFIG.fmp_api_key else 'No'}")
    
    print(f"\nDefault Symbols: {', '.join(TRADING_CONFIG.symbols[:5])}...")


def test_symbols():
    """Test the system with different symbols"""
    symbols = input("\nEnter symbols to test (comma-separated, e.g., AAPL,MSFT,GOOGL): ").strip()
    
    if not symbols:
        print("No symbols entered.")
        return
    
    symbol_list = [s.strip().upper() for s in symbols.split(",")]
    print(f"\nYou can test these symbols using:")
    print(f"  python main.py --mode multi-backtest --symbols {' '.join(symbol_list)}")
    print(f"\nOr for live trading:")
    print(f"  python main.py --mode live --symbols {' '.join(symbol_list)}")


if __name__ == "__main__":
    print("Welcome to the Automated Momentum Reversal Trading System!")
    
    # Check if user wants interactive mode
    mode = input("\nRun in interactive mode? (y/n): ").strip().lower()
    
    if mode == 'y' or mode == 'yes':
        interactive_demo()
    else:
        run_demo()
