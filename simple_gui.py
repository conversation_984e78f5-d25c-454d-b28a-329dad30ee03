"""
Simplified Desktop GUI for Automated Trading System
"""
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
from datetime import datetime
import queue

from stock_universe import StockUniverse
from mass_scanner import MassScanner
from config import TRADING_CONFIG


class SimpleTradingGUI:
    """Simplified GUI for the trading system"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Momentum Reversal Trading System")
        self.root.geometry("1200x800")
        
        # Data
        self.universe = []
        self.scan_results = []
        self.is_scanning = False
        self.message_queue = queue.Queue()
        
        self.create_widgets()
        self.process_messages()
        self.load_universe()
    
    def create_widgets(self):
        """Create GUI widgets"""
        # Title
        title_label = tk.Label(self.root, text="🎯 Momentum Reversal Trading System", 
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=10)
        
        # Status frame
        status_frame = tk.Frame(self.root)
        status_frame.pack(fill='x', padx=10, pady=5)
        
        self.status_label = tk.Label(status_frame, text="Status: Initializing...", 
                                    font=('Arial', 12))
        self.status_label.pack(side='left')
        
        # House Money Strategy frame
        hm_frame = tk.LabelFrame(self.root, text="House Money Strategy", 
                                font=('Arial', 12, 'bold'))
        hm_frame.pack(fill='x', padx=10, pady=5)
        
        self.daily_pnl_label = tk.Label(hm_frame, text="Daily P&L: $0.00 / $100.00 (0%)", 
                                       font=('Arial', 11))
        self.daily_pnl_label.pack(pady=5)
        
        self.phase_label = tk.Label(hm_frame, text="Phase: INITIAL_CAPITAL", 
                                   font=('Arial', 10))
        self.phase_label.pack()
        
        # Control buttons
        button_frame = tk.Frame(self.root)
        button_frame.pack(fill='x', padx=10, pady=10)
        
        self.scan_button = tk.Button(button_frame, text="🔍 Start Mass Scan", 
                                    command=self.start_scan, font=('Arial', 11, 'bold'),
                                    bg='#4CAF50', fg='white')
        self.scan_button.pack(side='left', padx=5)
        
        self.stop_button = tk.Button(button_frame, text="⏹ Stop Scan", 
                                    command=self.stop_scan, font=('Arial', 11, 'bold'),
                                    bg='#f44336', fg='white', state='disabled')
        self.stop_button.pack(side='left', padx=5)
        
        # Progress bar
        self.progress_var = tk.StringVar(value="Ready to scan")
        self.progress_label = tk.Label(self.root, textvariable=self.progress_var, 
                                      font=('Arial', 10))
        self.progress_label.pack(pady=5)
        
        # Results frame
        results_frame = tk.LabelFrame(self.root, text="Scan Results", 
                                     font=('Arial', 12, 'bold'))
        results_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Results listbox with scrollbar
        listbox_frame = tk.Frame(results_frame)
        listbox_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        self.results_listbox = tk.Listbox(listbox_frame, font=('Consolas', 10))
        scrollbar = tk.Scrollbar(listbox_frame, orient='vertical')
        
        self.results_listbox.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.results_listbox.yview)
        
        self.results_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # Log frame
        log_frame = tk.LabelFrame(self.root, text="System Log", 
                                 font=('Arial', 12, 'bold'))
        log_frame.pack(fill='x', padx=10, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=6, 
                                                 font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Initial log messages
        self.log_message("System initialized")
        self.log_message(f"Daily target: ${TRADING_CONFIG.daily_profit_target}")
        self.log_message(f"Initial profit lock: ${TRADING_CONFIG.initial_profit_lock}")
    
    def log_message(self, message: str):
        """Add message to log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # Keep log manageable
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 50:
            self.log_text.delete("1.0", "10.0")
    
    def load_universe(self):
        """Load stock universe"""
        def load_task():
            try:
                self.message_queue.put(("log", "Loading S&P 500 + $100B market cap stocks..."))
                universe_manager = StockUniverse()
                self.universe = universe_manager.build_universe()
                
                self.message_queue.put(("status", f"Loaded {len(self.universe)} stocks"))
                self.message_queue.put(("log", f"Ready to scan {len(self.universe)} stocks"))
                
            except Exception as e:
                self.message_queue.put(("error", f"Error loading universe: {e}"))
        
        threading.Thread(target=load_task, daemon=True).start()
    
    def start_scan(self):
        """Start mass scanning"""
        if self.is_scanning or not self.universe:
            return
        
        self.is_scanning = True
        self.scan_button.config(state='disabled')
        self.stop_button.config(state='normal')
        
        def scan_task():
            try:
                self.message_queue.put(("log", f"Starting scan of {len(self.universe)} stocks..."))
                
                scanner = MassScanner(max_workers=6)
                
                # Scan in smaller batches
                batch_size = 20
                batches = [self.universe[i:i+batch_size] for i in range(0, len(self.universe), batch_size)]
                all_results = []
                
                for i, batch in enumerate(batches):
                    if not self.is_scanning:
                        break
                    
                    progress = f"Scanning batch {i+1}/{len(batches)} ({len(all_results)} scanned)"
                    self.message_queue.put(("progress", progress))
                    
                    batch_results = scanner.scan_batch(batch)
                    all_results.extend(batch_results)
                    
                    # Update results
                    self.message_queue.put(("results", all_results.copy()))
                    
                    time.sleep(0.5)  # Brief pause
                
                if self.is_scanning:
                    self.message_queue.put(("scan_complete", all_results))
                    
            except Exception as e:
                self.message_queue.put(("error", f"Scan error: {e}"))
            finally:
                self.message_queue.put(("scan_finished", None))
        
        threading.Thread(target=scan_task, daemon=True).start()
    
    def stop_scan(self):
        """Stop scanning"""
        self.is_scanning = False
        self.scan_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.log_message("Scan stopped")
    
    def update_results_display(self, results):
        """Update results display"""
        self.results_listbox.delete(0, tk.END)
        
        # Add header
        header = f"{'Symbol':<8} {'Entry':<6} {'Reversals':<9} {'ATR':<6} {'Strength':<8} {'Price':<10}"
        self.results_listbox.insert(tk.END, header)
        self.results_listbox.insert(tk.END, "-" * 60)
        
        # Sort results
        successful = [r for r in results if r.success]
        successful.sort(key=lambda x: (x.entry_signals, x.pattern_strength), reverse=True)
        
        # Add results
        entry_count = 0
        for result in successful[:30]:  # Show top 30
            if result.entry_signals > 0:
                entry_count += 1
                prefix = "🎯 "
            elif result.momentum_reversals > 0:
                prefix = "📈 "
            else:
                prefix = "   "
            
            line = (f"{prefix}{result.symbol:<8} {result.entry_signals:<6} "
                   f"{result.momentum_reversals:<9} {result.atr_confirmations:<6} "
                   f"{result.pattern_strength:<8.2f} ${result.current_price:<9.2f}")
            
            self.results_listbox.insert(tk.END, line)
        
        # Update status
        if entry_count > 0:
            self.log_message(f"🎯 Found {entry_count} stocks with entry signals!")
    
    def process_messages(self):
        """Process messages from background threads"""
        try:
            while True:
                message_type, data = self.message_queue.get_nowait()
                
                if message_type == "log":
                    self.log_message(data)
                
                elif message_type == "status":
                    self.status_label.config(text=f"Status: {data}")
                
                elif message_type == "progress":
                    self.progress_var.set(data)
                
                elif message_type == "results":
                    self.scan_results = data
                    self.update_results_display(data)
                
                elif message_type == "scan_complete":
                    self.scan_results = data
                    self.update_results_display(data)
                    self.log_message("✅ Scan completed!")
                
                elif message_type == "scan_finished":
                    self.is_scanning = False
                    self.scan_button.config(state='normal')
                    self.stop_button.config(state='disabled')
                    self.progress_var.set("Scan finished")
                
                elif message_type == "error":
                    self.log_message(f"❌ {data}")
                    messagebox.showerror("Error", data)
                
        except queue.Empty:
            pass
        
        # Schedule next check
        self.root.after(100, self.process_messages)
    
    def run(self):
        """Start the GUI"""
        self.root.mainloop()


def main():
    """Main entry point"""
    try:
        app = SimpleTradingGUI()
        app.run()
    except Exception as e:
        print(f"Error starting GUI: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
