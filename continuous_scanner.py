"""
Continuous Real-Time Scanner with Progressive Pattern Monitoring
"""
import time
import threading
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Set, Optional, Tuple
# import schedule  # Optional - using manual time checking instead
from concurrent.futures import ThreadPoolExecutor, as_completed

from stock_universe import StockUniverse
from progressive_pattern_monitor import ProgressivePatternMonitor, PatternStage, PatternProgress
from market_data_provider import MarketDataManager
from automated_trading_system import AutomatedTradingSystem
from config import API_CONFIG, TRADING_CONFIG, SYSTEM_CONFIG


class ContinuousScanner:
    """Continuous real-time scanner with intelligent pattern monitoring"""
    
    def __init__(self, scan_interval_minutes: int = 2, max_workers: int = 8):
        self.scan_interval_minutes = scan_interval_minutes
        self.max_workers = max_workers
        self.logger = logging.getLogger(__name__)
        
        # Core components
        self.universe_manager = StockUniverse()
        self.pattern_monitor = ProgressivePatternMonitor()
        self.data_manager = MarketDataManager(
            fmp_api_key=API_CONFIG.fmp_api_key,
            alpaca_api_key=API_CONFIG.alpaca_api_key,
            alpaca_secret=API_CONFIG.alpaca_secret_key
        )
        
        # Trading system (optional)
        self.trading_system: Optional[AutomatedTradingSystem] = None
        self.auto_trading_enabled = False
        
        # State management
        self.is_running = False
        self.is_market_hours = False
        self.universe: List[str] = []
        self.scan_count = 0
        self.last_full_scan = None
        
        # Performance tracking
        self.scan_times: List[float] = []
        self.entry_signals_found = 0
        self.patterns_tracked = 0
        
        # Initialize
        self._setup_logging()
        self._load_universe()
        self._setup_scheduler()
    
    def _setup_logging(self):
        """Setup enhanced logging for continuous operation"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('continuous_scanner.log'),
                logging.StreamHandler()
            ]
        )
    
    def _load_universe(self):
        """Load the stock universe"""
        self.logger.info("Loading stock universe...")
        self.universe = self.universe_manager.build_universe()
        self.logger.info(f"Loaded {len(self.universe)} stocks for continuous scanning")
    
    def _setup_scheduler(self):
        """Setup market hours and scanning schedule"""
        # Using manual time checking instead of schedule library
        # Market hours: 9:30 AM - 4:00 PM ET (Monday-Friday)
        self.logger.info("Market hours: 9:30 AM - 4:00 PM ET (Monday-Friday)")
        pass  # We'll use _is_market_hours() method for time checking
    
    def _start_market_session(self):
        """Start market session"""
        self.is_market_hours = True
        self.pattern_monitor.reset_daily_stats()
        self.logger.info("🔔 Market session started - Beginning continuous scanning")
    
    def _end_market_session(self):
        """End market session"""
        self.is_market_hours = False
        self._log_daily_summary()
        self.logger.info("🔔 Market session ended - Stopping continuous scanning")
    
    def _is_market_hours(self) -> bool:
        """Check if currently in market hours"""
        now = datetime.now()
        weekday = now.weekday()  # 0=Monday, 6=Sunday
        
        # Only Monday-Friday
        if weekday >= 5:
            return False
        
        # Market hours: 9:30 AM - 4:00 PM
        market_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
        market_end = now.replace(hour=16, minute=0, second=0, microsecond=0)
        
        return market_start <= now <= market_end
    
    def start_continuous_scanning(self, enable_auto_trading: bool = False):
        """Start continuous scanning operation"""
        self.is_running = True
        self.auto_trading_enabled = enable_auto_trading
        
        if enable_auto_trading:
            try:
                self.trading_system = AutomatedTradingSystem()
                self.logger.info("🚀 Auto-trading enabled")
            except Exception as e:
                self.logger.error(f"Failed to initialize trading system: {e}")
                self.auto_trading_enabled = False
        
        self.logger.info("🔄 Starting continuous scanning system...")
        self.logger.info(f"Scan interval: {self.scan_interval_minutes} minutes")
        self.logger.info(f"Universe size: {len(self.universe)} stocks")
        
        # Start main scanning loop
        self._run_scanning_loop()
    
    def stop_continuous_scanning(self):
        """Stop continuous scanning"""
        self.is_running = False
        self.logger.info("🛑 Stopping continuous scanning system")
    
    def _run_scanning_loop(self):
        """Main continuous scanning loop"""
        while self.is_running:
            try:
                # Check if in market hours
                if self._is_market_hours():
                    self._execute_scan_cycle()
                else:
                    self.logger.info("Outside market hours - waiting...")
                    time.sleep(300)  # Wait 5 minutes when market closed
                    continue
                
                # Wait for next scan interval
                self.logger.info(f"⏳ Waiting {self.scan_interval_minutes} minutes until next scan...")
                time.sleep(self.scan_interval_minutes * 60)
                
            except KeyboardInterrupt:
                self.logger.info("Scanning stopped by user")
                break
            except Exception as e:
                self.logger.error(f"Error in scanning loop: {e}")
                time.sleep(60)  # Wait 1 minute before retrying
    
    def _execute_scan_cycle(self):
        """Execute a single scan cycle"""
        scan_start = time.time()
        self.scan_count += 1
        
        self.logger.info(f"🔍 Starting scan cycle #{self.scan_count}")
        
        # Step 1: Cleanup stale patterns
        self.pattern_monitor.cleanup_stale_patterns()
        
        # Step 2: Determine scan targets
        scan_targets = self._determine_scan_targets()
        
        self.logger.info(f"Scanning {len(scan_targets)} stocks this cycle")
        
        # Step 3: Execute parallel scanning
        new_patterns, updated_patterns = self._scan_stocks_parallel(scan_targets)
        
        # Step 4: Process results
        completed_patterns = self.pattern_monitor.get_completed_patterns()
        
        # Step 5: Handle trading signals
        if completed_patterns and self.auto_trading_enabled:
            self._handle_trading_signals(completed_patterns)
        
        # Step 6: Log cycle results
        scan_time = time.time() - scan_start
        self.scan_times.append(scan_time)
        self._log_scan_cycle_results(scan_time, new_patterns, updated_patterns, completed_patterns)
        
        self.last_full_scan = datetime.now()
    
    def _determine_scan_targets(self) -> List[str]:
        """Intelligently determine which stocks to scan this cycle"""
        targets = set()
        
        # Always scan current watch list (high priority)
        watch_list_symbols = set(self.pattern_monitor.watch_list.keys())
        targets.update(watch_list_symbols)
        
        # Every 5th scan, do a full universe scan to find new opportunities
        if self.scan_count % 5 == 0:
            self.logger.info("🌍 Full universe scan cycle")
            targets.update(self.universe)
        else:
            # Focus on high-volume, volatile stocks for new opportunities
            high_priority_stocks = self._get_high_priority_stocks()
            targets.update(high_priority_stocks)
        
        return list(targets)
    
    def _get_high_priority_stocks(self) -> List[str]:
        """Get high-priority stocks for focused scanning"""
        # Major ETFs and high-volume stocks
        priority_stocks = [
            # ETFs
            "SPY", "QQQ", "IWM", "XLF", "XLK", "XLE", "XLI", "XLV", "XLP", "XLU",
            
            # Mega caps
            "AAPL", "MSFT", "GOOGL", "AMZN", "NVDA", "META", "TSLA", "BRK.B",
            
            # High volatility
            "AMD", "NFLX", "CRM", "ADBE", "INTC", "BABA", "COIN", "PLTR",
            
            # Financial
            "JPM", "BAC", "WFC", "GS", "MS", "C",
            
            # Energy
            "XOM", "CVX", "COP", "SLB"
        ]
        
        return [s for s in priority_stocks if s in self.universe]
    
    def _scan_stocks_parallel(self, symbols: List[str]) -> Tuple[int, int]:
        """Scan stocks in parallel and update pattern monitor"""
        new_patterns = 0
        updated_patterns = 0
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit scanning tasks
            future_to_symbol = {
                executor.submit(self._scan_single_stock, symbol): symbol 
                for symbol in symbols
            }
            
            # Process results as they complete
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    progress = future.result(timeout=30)
                    if progress:
                        if symbol in self.pattern_monitor.watch_list:
                            updated_patterns += 1
                        else:
                            # Add to watch list if showing early pattern development
                            if progress.stage != PatternStage.FAILED:
                                self.pattern_monitor.add_to_watch_list(symbol, progress)
                                new_patterns += 1
                        
                except Exception as e:
                    self.logger.warning(f"Error scanning {symbol}: {e}")
        
        return new_patterns, updated_patterns
    
    def _scan_single_stock(self, symbol: str) -> Optional[PatternProgress]:
        """Scan a single stock for pattern development"""
        try:
            # Get recent market data
            market_data = self.data_manager.get_market_data(
                symbol=symbol,
                source="fmp",
                timeframe="1min",
                days=1
            )
            
            if not market_data or len(market_data) < 10:
                return None
            
            # Analyze latest bar
            latest_data = market_data[-1]
            progress = self.pattern_monitor.analyze_stock_progression(symbol, latest_data)
            
            return progress
            
        except Exception as e:
            self.logger.debug(f"Error scanning {symbol}: {e}")
            return None
    
    def _handle_trading_signals(self, completed_patterns: List[PatternProgress]):
        """Handle completed patterns for trading"""
        for pattern in completed_patterns:
            try:
                self.logger.info(f"🎯 ENTRY SIGNAL: {pattern.symbol} - Pattern completed!")
                self.entry_signals_found += 1
                
                if self.trading_system:
                    # Create trading signal and execute
                    # This would integrate with the existing trading system
                    self.logger.info(f"Executing trade for {pattern.symbol}")
                
            except Exception as e:
                self.logger.error(f"Error handling trading signal for {pattern.symbol}: {e}")
    
    def _log_scan_cycle_results(self, scan_time: float, new_patterns: int, 
                               updated_patterns: int, completed_patterns: List[PatternProgress]):
        """Log results of scan cycle"""
        stats = self.pattern_monitor.get_statistics()
        
        self.logger.info(f"✅ Scan cycle #{self.scan_count} completed in {scan_time:.1f}s")
        self.logger.info(f"   New patterns: {new_patterns}")
        self.logger.info(f"   Updated patterns: {updated_patterns}")
        self.logger.info(f"   Completed patterns: {len(completed_patterns)}")
        self.logger.info(f"   Watch list size: {stats['current_watch_list']}")
        
        if completed_patterns:
            symbols = [p.symbol for p in completed_patterns]
            self.logger.info(f"   🎯 Entry signals: {', '.join(symbols)}")
        
        # Log watch list by stage
        by_stage = self.pattern_monitor.get_watch_list_by_stage()
        stage_summary = []
        for stage, patterns in by_stage.items():
            if patterns:
                stage_summary.append(f"{stage.value}: {len(patterns)}")
        
        if stage_summary:
            self.logger.info(f"   Stage breakdown: {', '.join(stage_summary)}")
    
    def _log_daily_summary(self):
        """Log end-of-day summary"""
        stats = self.pattern_monitor.get_statistics()
        avg_scan_time = sum(self.scan_times) / len(self.scan_times) if self.scan_times else 0
        
        self.logger.info("📊 DAILY SUMMARY")
        self.logger.info(f"   Total scans: {self.scan_count}")
        self.logger.info(f"   Average scan time: {avg_scan_time:.1f}s")
        self.logger.info(f"   Entry signals found: {self.entry_signals_found}")
        self.logger.info(f"   Patterns completed: {stats['patterns_completed_today']}")
        self.logger.info(f"   Patterns failed: {stats['patterns_failed_today']}")
        self.logger.info(f"   Completion rate: {stats['completion_rate']:.1f}%")
    
    def get_current_status(self) -> Dict[str, any]:
        """Get current scanner status"""
        stats = self.pattern_monitor.get_statistics()
        priority_list = self.pattern_monitor.get_priority_watch_list(10)
        
        return {
            "is_running": self.is_running,
            "is_market_hours": self._is_market_hours(),
            "scan_count": self.scan_count,
            "last_scan": self.last_full_scan.isoformat() if self.last_full_scan else None,
            "watch_list_size": stats['current_watch_list'],
            "entry_signals_today": self.entry_signals_found,
            "patterns_completed_today": stats['patterns_completed_today'],
            "top_opportunities": [
                {
                    "symbol": p.symbol,
                    "stage": p.stage.value,
                    "strength": p.pattern_strength,
                    "declining_bars": p.consecutive_declining_bars
                }
                for p in priority_list[:5]
            ]
        }


def main():
    """Main entry point for continuous scanner"""
    print("🔄 Starting Continuous Real-Time Scanner")
    print("=" * 60)
    
    scanner = ContinuousScanner(scan_interval_minutes=2)
    
    try:
        scanner.start_continuous_scanning(enable_auto_trading=False)
    except KeyboardInterrupt:
        print("\n🛑 Stopping scanner...")
        scanner.stop_continuous_scanning()


if __name__ == "__main__":
    main()
