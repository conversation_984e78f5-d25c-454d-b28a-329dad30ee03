"""
Simple example demonstrating the automated trading system.
"""
from datetime import datetime, <PERSON>elta
import numpy as np
from data_models import MarketData, MomentumColor, ATRSignal
from technical_indicators import TechnicalAnalyzer
from pattern_detector import MomentumReversalDetector
from trading_engine import TradingEngine


def create_sample_market_data():
    """Create sample market data with a momentum reversal pattern"""
    print("Creating sample market data...")
    
    base_time = datetime.now() - timedelta(hours=1)
    market_data = []
    base_price = 100.0
    
    # Phase 1: Declining momentum (red bars)
    print("  Phase 1: Creating declining red momentum...")
    for i in range(8):  # 8 declining bars
        decline = 0.5 + (i * 0.2)  # Increasing decline
        price = base_price - decline
        
        data = MarketData(
            timestamp=base_time + timedelta(minutes=i),
            open=base_price,
            high=base_price + 0.1,
            low=price - 0.1,
            close=price,
            volume=1000 + i * 100
        )
        market_data.append(data)
        base_price = price
    
    # Phase 2: Momentum reversal (yellow/green bars)
    print("  Phase 2: Creating momentum reversal...")
    for i in range(5):  # 5 recovery bars
        recovery = 0.3 + (i * 0.1)
        price = base_price + recovery
        
        data = MarketData(
            timestamp=base_time + timedelta(minutes=8 + i),
            open=base_price,
            high=price + 0.2,
            low=base_price - 0.1,
            close=price,
            volume=1200 + i * 50
        )
        market_data.append(data)
        base_price = price
    
    print(f"Created {len(market_data)} bars of sample data")
    return market_data


def demonstrate_pattern_detection():
    """Demonstrate the pattern detection system"""
    print("\n" + "="*60)
    print("MOMENTUM REVERSAL PATTERN DETECTION DEMO")
    print("="*60)
    
    # Create components
    technical_analyzer = TechnicalAnalyzer(momentum_period=5, atr_period=5)  # Shorter periods for demo
    pattern_detector = MomentumReversalDetector(min_declining_bars=4)
    trading_engine = TradingEngine(initial_capital=10000, position_size=0.1)
    
    # Create sample data
    market_data = create_sample_market_data()
    
    print(f"\nProcessing {len(market_data)} bars...")
    print("-" * 60)
    
    entry_signals = 0
    exit_signals = 0
    
    for i, data_point in enumerate(market_data):
        # Perform technical analysis
        technical_data = technical_analyzer.analyze(data_point)
        
        # Detect patterns
        pattern_signal = pattern_detector.detect_pattern(technical_data)
        
        # Display key information
        momentum = technical_data['momentum']
        momentum_color = technical_data['momentum_color'].value
        atr_confirmed = technical_data['atr_confirmed']
        
        print(f"Bar {i+1:2d}: Price=${data_point.close:6.2f} | "
              f"Momentum={momentum:6.2f} ({momentum_color:6s}) | "
              f"ATR Confirmed={str(atr_confirmed):5s}")
        
        # Check for pattern components
        if pattern_signal.declining_red_momentum:
            print(f"        📉 Declining red momentum detected")
        
        if pattern_signal.momentum_reversal:
            print(f"        🔄 Momentum reversal detected")
        
        if pattern_signal.atr_confirmation:
            print(f"        ✅ ATR confirmation")
        
        # Check for trading signals
        if pattern_signal.entry_signal:
            entry_signals += 1
            print(f"        🟢 ENTRY SIGNAL - All conditions met!")
            
            # Simulate trade entry
            from data_models import TradingSignal, TechnicalIndicators, PositionStatus
            
            indicators = TechnicalIndicators(
                momentum=momentum,
                momentum_color=technical_data['momentum_color'],
                atr_value=technical_data['atr_value'],
                atr_signal=technical_data['atr_signal'],
                atr_confirmed=atr_confirmed
            )
            
            signal = TradingSignal(
                timestamp=data_point.timestamp,
                market_data=data_point,
                indicators=indicators,
                pattern=pattern_signal,
                position_status=PositionStatus.OUT
            )
            
            trading_engine.process_signal(signal)
        
        elif pattern_signal.exit_signal:
            exit_signals += 1
            print(f"        🔴 EXIT SIGNAL")
    
    # Display results
    print("\n" + "="*60)
    print("RESULTS SUMMARY")
    print("="*60)
    
    print(f"Total Bars Processed: {len(market_data)}")
    print(f"Entry Signals: {entry_signals}")
    print(f"Exit Signals: {exit_signals}")
    
    # Trading results
    trades = trading_engine.trades
    if trades:
        print(f"\nTrading Performance:")
        print(f"  Trades Executed: {len(trades)}")
        
        total_pnl = sum(trade.pnl for trade in trades)
        winning_trades = sum(1 for trade in trades if trade.pnl > 0)
        win_rate = winning_trades / len(trades) if trades else 0
        
        print(f"  Total P&L: ${total_pnl:.2f}")
        print(f"  Win Rate: {win_rate:.1%}")
        print(f"  Final Capital: ${trading_engine.current_capital:.2f}")
        
        print(f"\nTrade Details:")
        for i, trade in enumerate(trades, 1):
            print(f"  Trade {i}: ${trade.entry_price:.2f} → ${trade.exit_price:.2f} "
                  f"(P&L: ${trade.pnl:.2f})")
    else:
        print(f"\nNo trades were executed.")
        print("This could happen if:")
        print("  - All three conditions weren't met simultaneously")
        print("  - ATR confirmation wasn't achieved")
        print("  - Pattern wasn't strong enough")
    
    # Pattern analysis
    pattern_strength = pattern_detector.get_pattern_strength()
    print(f"\nPattern Analysis:")
    print(f"  Pattern Strength: {pattern_strength:.2f} (0.0 to 1.0)")
    print(f"  Momentum History Length: {len(pattern_detector.momentum_history)}")
    print(f"  Color History: {[c.value for c in list(pattern_detector.color_history)[-5:]]}")


def demonstrate_system_components():
    """Demonstrate individual system components"""
    print("\n" + "="*60)
    print("SYSTEM COMPONENTS DEMONSTRATION")
    print("="*60)
    
    # 1. Technical Indicators
    print("\n1. Technical Indicators:")
    print("-" * 30)
    
    analyzer = TechnicalAnalyzer()
    sample_data = MarketData(
        timestamp=datetime.now(),
        open=100, high=101, low=99, close=100.5,
        volume=1000
    )
    
    result = analyzer.analyze(sample_data)
    print(f"   Momentum: {result['momentum']:.2f}")
    print(f"   Momentum Color: {result['momentum_color'].value}")
    print(f"   ATR Value: {result['atr_value']:.2f}")
    print(f"   ATR Signal: {result['atr_signal'].value}")
    print(f"   ATR Confirmed: {result['atr_confirmed']}")
    
    # 2. Pattern Detection
    print("\n2. Pattern Detection:")
    print("-" * 30)
    
    detector = MomentumReversalDetector()
    
    # Simulate pattern
    detector.update_history(-5, MomentumColor.RED, True)
    detector.update_history(-6, MomentumColor.RED, True)
    detector.update_history(-7, MomentumColor.RED, True)
    detector.update_history(-8, MomentumColor.RED, True)
    detector.update_history(1, MomentumColor.YELLOW, False)
    
    print(f"   Declining Red Momentum: {detector.detect_declining_red_momentum_phase()}")
    print(f"   Momentum Reversal: {detector.detect_momentum_reversal_signal()}")
    print(f"   Pattern Strength: {detector.get_pattern_strength():.2f}")
    
    # 3. Trading Engine
    print("\n3. Trading Engine:")
    print("-" * 30)
    
    engine = TradingEngine(initial_capital=10000, position_size=0.1)
    print(f"   Initial Capital: ${engine.initial_capital:,.2f}")
    print(f"   Position Size: {engine.position_size:.1%}")
    print(f"   Can Enter Position: {engine.can_enter_position()}")
    
    position_size = engine.calculate_position_size(100.0)
    print(f"   Position Size at $100: {position_size} shares")


def main():
    """Main demonstration function"""
    print("AUTOMATED MOMENTUM REVERSAL TRADING SYSTEM")
    print("Comprehensive Demonstration")
    print("="*60)
    
    try:
        # Demonstrate system components
        demonstrate_system_components()
        
        # Demonstrate pattern detection
        demonstrate_pattern_detection()
        
        print("\n" + "="*60)
        print("DEMONSTRATION COMPLETED SUCCESSFULLY!")
        print("="*60)
        
        print("\nKey Features Demonstrated:")
        print("✓ Technical indicator calculations (Momentum & ATR)")
        print("✓ Pattern detection (Declining red momentum + reversal)")
        print("✓ ATR confirmation system")
        print("✓ Automated trade execution")
        print("✓ Position management")
        print("✓ Performance tracking")
        
        print(f"\nTo run with real market data:")
        print(f"  python main.py --mode backtest --symbol SPY")
        print(f"  python main.py --mode live --symbols SPY QQQ")
        
    except Exception as e:
        print(f"Error during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
