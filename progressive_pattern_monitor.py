"""
Progressive Pattern Monitor - Tracks stocks through momentum reversal setup stages
"""
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Set, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

from data_models import MarketData, MomentumColor, ATRSignal
from technical_indicators import <PERSON>Analyzer
from pattern_detector import MomentumReversalDetector


class PatternStage(Enum):
    """Stages of momentum reversal pattern development"""
    STAGE_1 = "early_decline"      # 1-3 consecutive red declining bars
    STAGE_2 = "building_pattern"   # Building toward 4+ bar requirement
    STAGE_3 = "momentum_reversal"  # Red to yellow transition detected
    STAGE_4 = "atr_confirmed"      # ATR confirmation active
    COMPLETE = "pattern_complete"   # All criteria met - ready to trade
    FAILED = "pattern_failed"      # Pattern broken or failed


@dataclass
class PatternProgress:
    """Tracks a stock's progress through pattern stages"""
    symbol: str
    stage: PatternStage
    consecutive_declining_bars: int = 0
    last_momentum_color: MomentumColor = MomentumColor.YELLOW
    momentum_reversal_detected: bool = False
    atr_confirmed: bool = False
    atr_signal: ATRSignal = ATRSignal.NEUTRAL
    pattern_strength: float = 0.0
    current_price: float = 0.0
    last_update: datetime = field(default_factory=datetime.now)
    entry_timestamp: Optional[datetime] = None
    failure_reason: str = ""
    
    # Pattern history tracking
    momentum_history: List[float] = field(default_factory=list)
    color_history: List[MomentumColor] = field(default_factory=list)
    declining_history: List[bool] = field(default_factory=list)


class ProgressivePatternMonitor:
    """Monitors stocks through progressive pattern development stages"""
    
    def __init__(self, min_declining_bars: int = 4, max_watch_age_minutes: int = 60):
        self.min_declining_bars = min_declining_bars
        self.max_watch_age_minutes = max_watch_age_minutes
        self.logger = logging.getLogger(__name__)
        
        # Watch lists by stage
        self.watch_list: Dict[str, PatternProgress] = {}
        self.completed_patterns: Dict[str, PatternProgress] = {}
        self.failed_patterns: Dict[str, PatternProgress] = {}
        
        # Technical analysis components
        self.technical_analyzer = TechnicalAnalyzer()
        self.pattern_detector = MomentumReversalDetector(min_declining_bars)
        
        # Statistics
        self.total_stocks_monitored = 0
        self.patterns_completed_today = 0
        self.patterns_failed_today = 0
    
    def analyze_stock_progression(self, symbol: str, market_data: MarketData) -> Optional[PatternProgress]:
        """Analyze a stock's progression through pattern stages"""
        try:
            # Perform technical analysis
            technical_data = self.technical_analyzer.analyze(market_data)

            momentum = technical_data['momentum']
            momentum_color = technical_data['momentum_color']
            is_declining = technical_data['is_declining_momentum']
            atr_signal = technical_data['atr_signal']
            atr_confirmed = technical_data['atr_confirmed']

            # For testing/debugging - ensure we have valid data
            if momentum == 0 and len(self.technical_analyzer.market_history) < 5:
                # Not enough data for proper analysis yet
                return None
            
            # Get or create pattern progress
            if symbol in self.watch_list:
                progress = self.watch_list[symbol]
            else:
                progress = PatternProgress(symbol=symbol, stage=PatternStage.STAGE_1)
            
            # Update basic data
            progress.current_price = market_data.close
            progress.last_update = datetime.now()
            
            # Update history (keep last 20 bars)
            progress.momentum_history.append(momentum)
            progress.color_history.append(momentum_color)
            progress.declining_history.append(is_declining)
            
            if len(progress.momentum_history) > 20:
                progress.momentum_history.pop(0)
                progress.color_history.pop(0)
                progress.declining_history.pop(0)
            
            # Analyze current pattern state
            self._update_pattern_progression(progress, momentum_color, is_declining, atr_signal, atr_confirmed)
            
            return progress
            
        except Exception as e:
            self.logger.error(f"Error analyzing {symbol}: {e}")
            return None
    
    def _update_pattern_progression(self, progress: PatternProgress, 
                                  momentum_color: MomentumColor, is_declining: bool,
                                  atr_signal: ATRSignal, atr_confirmed: bool):
        """Update pattern progression based on current bar analysis"""
        
        # Update consecutive declining bars count
        if momentum_color == MomentumColor.RED and is_declining:
            if progress.last_momentum_color == MomentumColor.RED:
                progress.consecutive_declining_bars += 1
            else:
                progress.consecutive_declining_bars = 1
        else:
            # Check if this breaks the pattern
            if progress.stage in [PatternStage.STAGE_1, PatternStage.STAGE_2]:
                if progress.consecutive_declining_bars < self.min_declining_bars:
                    self._mark_pattern_failed(progress, "Declining sequence broken before reaching minimum bars")
                    return
        
        # Update momentum reversal detection
        if (progress.last_momentum_color == MomentumColor.RED and 
            momentum_color == MomentumColor.YELLOW):
            progress.momentum_reversal_detected = True
        
        # Update ATR status
        progress.atr_signal = atr_signal
        progress.atr_confirmed = atr_confirmed
        
        # Update last momentum color
        progress.last_momentum_color = momentum_color
        
        # Determine current stage
        self._determine_pattern_stage(progress)
        
        # Calculate pattern strength
        progress.pattern_strength = self._calculate_pattern_strength(progress)
    
    def _determine_pattern_stage(self, progress: PatternProgress):
        """Determine the current stage of pattern development"""
        declining_bars = progress.consecutive_declining_bars
        
        # Check for pattern completion (all criteria met)
        if (declining_bars >= self.min_declining_bars and 
            progress.momentum_reversal_detected and 
            progress.atr_confirmed and 
            progress.atr_signal == ATRSignal.BLUE):
            
            progress.stage = PatternStage.COMPLETE
            if progress.entry_timestamp is None:
                progress.entry_timestamp = datetime.now()
            return
        
        # Check for ATR confirmation (Stage 4)
        if (declining_bars >= self.min_declining_bars and 
            progress.momentum_reversal_detected and 
            progress.atr_confirmed):
            progress.stage = PatternStage.STAGE_4
            return
        
        # Check for momentum reversal (Stage 3)
        if (declining_bars >= self.min_declining_bars and 
            progress.momentum_reversal_detected):
            progress.stage = PatternStage.STAGE_3
            return
        
        # Check for building pattern (Stage 2)
        if declining_bars >= self.min_declining_bars:
            progress.stage = PatternStage.STAGE_2
            return
        
        # Early decline stage (Stage 1)
        if declining_bars >= 1 and declining_bars < self.min_declining_bars:
            progress.stage = PatternStage.STAGE_1
            return
        
        # Pattern hasn't started or has failed
        if declining_bars == 0:
            self._mark_pattern_failed(progress, "No declining momentum detected")
    
    def _calculate_pattern_strength(self, progress: PatternProgress) -> float:
        """Calculate pattern strength score (0.0 to 1.0)"""
        strength_factors = []
        
        # Factor 1: Declining bars progress (0.0 to 0.4)
        declining_strength = min(progress.consecutive_declining_bars / 8.0, 0.4)
        strength_factors.append(declining_strength)
        
        # Factor 2: Momentum reversal (0.0 to 0.3)
        reversal_strength = 0.3 if progress.momentum_reversal_detected else 0.0
        strength_factors.append(reversal_strength)
        
        # Factor 3: ATR confirmation (0.0 to 0.3)
        atr_strength = 0.3 if progress.atr_confirmed else 0.0
        strength_factors.append(atr_strength)
        
        return sum(strength_factors)
    
    def _mark_pattern_failed(self, progress: PatternProgress, reason: str):
        """Mark a pattern as failed and remove from watch list"""
        progress.stage = PatternStage.FAILED
        progress.failure_reason = reason
        
        # Move to failed patterns
        if progress.symbol in self.watch_list:
            self.failed_patterns[progress.symbol] = self.watch_list.pop(progress.symbol)
            self.patterns_failed_today += 1
            
        self.logger.debug(f"{progress.symbol} pattern failed: {reason}")
    
    def add_to_watch_list(self, symbol: str, progress: PatternProgress):
        """Add stock to watch list"""
        if progress.stage != PatternStage.FAILED:
            self.watch_list[symbol] = progress
            self.total_stocks_monitored += 1
            
            self.logger.info(f"Added {symbol} to watch list - Stage: {progress.stage.value}")
    
    def remove_from_watch_list(self, symbol: str, reason: str = "Manual removal"):
        """Remove stock from watch list"""
        if symbol in self.watch_list:
            progress = self.watch_list.pop(symbol)
            self.logger.info(f"Removed {symbol} from watch list: {reason}")
    
    def get_completed_patterns(self) -> List[PatternProgress]:
        """Get all completed patterns ready for trading"""
        completed = []
        for symbol, progress in self.watch_list.items():
            if progress.stage == PatternStage.COMPLETE:
                completed.append(progress)
                # Move to completed patterns
                self.completed_patterns[symbol] = self.watch_list.pop(symbol)
                self.patterns_completed_today += 1
        
        return completed
    
    def get_watch_list_by_stage(self) -> Dict[PatternStage, List[PatternProgress]]:
        """Get watch list organized by pattern stage"""
        by_stage = {stage: [] for stage in PatternStage}
        
        for progress in self.watch_list.values():
            by_stage[progress.stage].append(progress)
        
        return by_stage
    
    def cleanup_stale_patterns(self):
        """Remove patterns that are too old or have failed"""
        current_time = datetime.now()
        stale_symbols = []
        
        for symbol, progress in self.watch_list.items():
            age_minutes = (current_time - progress.last_update).total_seconds() / 60
            
            if age_minutes > self.max_watch_age_minutes:
                stale_symbols.append(symbol)
                self._mark_pattern_failed(progress, f"Pattern aged out ({age_minutes:.1f} minutes)")
        
        for symbol in stale_symbols:
            if symbol in self.watch_list:
                self.watch_list.pop(symbol)
        
        if stale_symbols:
            self.logger.info(f"Cleaned up {len(stale_symbols)} stale patterns")
    
    def get_priority_watch_list(self, max_count: int = 20) -> List[PatternProgress]:
        """Get prioritized watch list (closest to completion)"""
        # Sort by stage progression and pattern strength
        stage_priority = {
            PatternStage.COMPLETE: 5,
            PatternStage.STAGE_4: 4,
            PatternStage.STAGE_3: 3,
            PatternStage.STAGE_2: 2,
            PatternStage.STAGE_1: 1
        }
        
        sorted_patterns = sorted(
            self.watch_list.values(),
            key=lambda p: (stage_priority.get(p.stage, 0), p.pattern_strength),
            reverse=True
        )
        
        return sorted_patterns[:max_count]
    
    def get_statistics(self) -> Dict[str, any]:
        """Get monitoring statistics"""
        by_stage = self.get_watch_list_by_stage()
        
        return {
            "total_monitored": self.total_stocks_monitored,
            "current_watch_list": len(self.watch_list),
            "patterns_completed_today": self.patterns_completed_today,
            "patterns_failed_today": self.patterns_failed_today,
            "by_stage": {
                stage.value: len(patterns) 
                for stage, patterns in by_stage.items()
            },
            "completion_rate": (
                self.patterns_completed_today / 
                max(self.patterns_completed_today + self.patterns_failed_today, 1)
            ) * 100
        }
    
    def reset_daily_stats(self):
        """Reset daily statistics"""
        self.patterns_completed_today = 0
        self.patterns_failed_today = 0
        self.completed_patterns.clear()
        self.failed_patterns.clear()
        self.logger.info("Daily statistics reset")


def main():
    """Test the progressive pattern monitor"""
    print("Testing Progressive Pattern Monitor...")
    
    monitor = ProgressivePatternMonitor()
    
    # Simulate some pattern progression
    test_symbols = ["AAPL", "MSFT", "GOOGL"]
    
    for symbol in test_symbols:
        # Create mock market data
        market_data = MarketData(
            timestamp=datetime.now(),
            open=100.0,
            high=101.0,
            low=99.0,
            close=100.0,
            volume=1000
        )
        
        progress = monitor.analyze_stock_progression(symbol, market_data)
        if progress:
            monitor.add_to_watch_list(symbol, progress)
    
    # Display results
    stats = monitor.get_statistics()
    print(f"Statistics: {stats}")
    
    priority_list = monitor.get_priority_watch_list()
    print(f"Priority watch list: {len(priority_list)} stocks")


if __name__ == "__main__":
    main()
