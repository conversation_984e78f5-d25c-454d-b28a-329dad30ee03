"""
Test script for the automated trading system.
"""
import unittest
from datetime import datetime, timedelta
import numpy as np

from data_models import MarketData, MomentumColor, ATRSignal
from technical_indicators import TechnicalAnalyzer
from pattern_detector import MomentumReversalDetector
from trading_engine import TradingEngine
from automated_trading_system import AutomatedTradingSystem


class TestTechnicalIndicators(unittest.TestCase):
    """Test technical indicators"""
    
    def setUp(self):
        self.analyzer = TechnicalAnalyzer()
    
    def test_momentum_calculation(self):
        """Test momentum calculation"""
        # Create sample market data
        base_time = datetime.now()
        market_data = []
        
        # Create declining price series
        prices = [100, 99, 98, 97, 96, 95, 94, 93, 92, 91, 90, 89, 88, 87, 86]
        
        for i, price in enumerate(prices):
            data = MarketData(
                timestamp=base_time + timedelta(minutes=i),
                open=price,
                high=price + 0.5,
                low=price - 0.5,
                close=price,
                volume=1000
            )
            market_data.append(data)
        
        # Analyze last data point
        result = self.analyzer.analyze(market_data[-1])
        
        # Should have negative momentum (declining prices)
        self.assertLess(result['momentum'], 0)
        self.assertEqual(result['momentum_color'], MomentumColor.RED)
    
    def test_atr_calculation(self):
        """Test ATR calculation"""
        # Create sample market data with volatility
        base_time = datetime.now()
        market_data = []
        
        for i in range(20):
            price = 100 + np.sin(i * 0.5) * 5  # Oscillating prices
            data = MarketData(
                timestamp=base_time + timedelta(minutes=i),
                open=price,
                high=price + 2,
                low=price - 2,
                close=price,
                volume=1000
            )
            market_data.append(data)
        
        # Analyze last data point
        result = self.analyzer.analyze(market_data[-1])
        
        # Should have positive ATR
        self.assertGreater(result['atr_value'], 0)
        self.assertIn(result['atr_signal'], [ATRSignal.BLUE, ATRSignal.RED, ATRSignal.NEUTRAL])


class TestPatternDetector(unittest.TestCase):
    """Test pattern detection"""
    
    def setUp(self):
        self.detector = MomentumReversalDetector(min_declining_bars=4)
    
    def test_declining_red_momentum_detection(self):
        """Test declining red momentum phase detection"""
        # Simulate declining red momentum
        for i in range(5):
            momentum = -5 - i  # Increasingly negative
            self.detector.update_history(momentum, MomentumColor.RED, True)
        
        # Should detect declining red momentum
        self.assertTrue(self.detector.detect_declining_red_momentum_phase())
    
    def test_momentum_reversal_detection(self):
        """Test momentum reversal signal detection"""
        # Setup with red momentum
        self.detector.update_history(-5, MomentumColor.RED, True)
        # Add yellow momentum (reversal)
        self.detector.update_history(1, MomentumColor.YELLOW, False)
        
        # Should detect reversal
        self.assertTrue(self.detector.detect_momentum_reversal_signal())
    
    def test_pattern_strength_calculation(self):
        """Test pattern strength calculation"""
        # Add some declining red momentum
        for i in range(6):
            momentum = -3 - i * 0.5
            self.detector.update_history(momentum, MomentumColor.RED, True)
        
        strength = self.detector.get_pattern_strength()
        self.assertGreater(strength, 0)
        self.assertLessEqual(strength, 1.0)


class TestTradingEngine(unittest.TestCase):
    """Test trading engine"""
    
    def setUp(self):
        self.engine = TradingEngine(initial_capital=10000, position_size=0.1)
    
    def test_position_size_calculation(self):
        """Test position size calculation"""
        entry_price = 100.0
        size = self.engine.calculate_position_size(entry_price)
        
        # Should be 10% of capital / price
        expected_size = int((10000 * 0.1) / entry_price)
        self.assertEqual(size, expected_size)
    
    def test_capital_management(self):
        """Test capital management"""
        initial_capital = self.engine.current_capital
        
        # Create mock trading signal
        from data_models import TradingSignal, PatternSignal
        
        market_data = MarketData(
            timestamp=datetime.now(),
            open=100, high=101, low=99, close=100,
            volume=1000
        )
        
        pattern = PatternSignal(
            declining_red_momentum=True,
            momentum_reversal=True,
            atr_confirmation=True,
            bar_pattern_detected=True,
            entry_signal=True,
            exit_signal=False
        )
        
        signal = TradingSignal(
            timestamp=datetime.now(),
            market_data=market_data,
            indicators=None,
            pattern=pattern,
            position_status=None
        )
        
        # Enter position
        success = self.engine.enter_position(signal)
        self.assertTrue(success)
        self.assertIsNotNone(self.engine.current_position)
        self.assertLess(self.engine.current_capital, initial_capital)


class TestSystemIntegration(unittest.TestCase):
    """Test system integration"""
    
    def setUp(self):
        # Mock the system to avoid API calls
        self.system = None
    
    def test_system_initialization(self):
        """Test system initialization"""
        try:
            # This might fail due to API validation, which is expected in test
            system = AutomatedTradingSystem()
            self.assertIsNotNone(system)
        except ValueError:
            # Expected if API keys are not properly configured for testing
            pass
    
    def test_signal_processing_logic(self):
        """Test signal processing logic without API calls"""
        # Create mock components
        analyzer = TechnicalAnalyzer()
        detector = MomentumReversalDetector()
        engine = TradingEngine()
        
        # Create sample market data
        market_data = MarketData(
            timestamp=datetime.now(),
            open=100, high=101, low=99, close=100,
            volume=1000
        )
        
        # This tests the core logic without API dependencies
        technical_data = analyzer.analyze(market_data)
        pattern_signal = detector.detect_pattern(technical_data)
        
        # Verify components work together
        self.assertIsNotNone(technical_data)
        self.assertIsNotNone(pattern_signal)


def create_sample_data(num_bars: int = 50) -> list:
    """Create sample market data for testing"""
    base_time = datetime.now() - timedelta(minutes=num_bars)
    market_data = []
    
    # Create realistic price movement
    base_price = 100.0
    
    for i in range(num_bars):
        # Add some randomness and trend
        price_change = np.random.normal(0, 0.5) + (i * 0.01)  # Slight upward trend
        price = base_price + price_change
        
        data = MarketData(
            timestamp=base_time + timedelta(minutes=i),
            open=price,
            high=price + abs(np.random.normal(0, 0.3)),
            low=price - abs(np.random.normal(0, 0.3)),
            close=price + np.random.normal(0, 0.2),
            volume=int(np.random.normal(1000, 200))
        )
        market_data.append(data)
        base_price = data.close
    
    return market_data


def run_integration_test():
    """Run a comprehensive integration test"""
    print("Running Integration Test...")
    print("=" * 50)
    
    # Create sample data
    sample_data = create_sample_data(100)
    print(f"Created {len(sample_data)} sample data points")
    
    # Test technical analysis
    analyzer = TechnicalAnalyzer()
    detector = MomentumReversalDetector()
    engine = TradingEngine(initial_capital=10000)
    
    signals = []
    
    for data_point in sample_data:
        # Analyze
        technical_data = analyzer.analyze(data_point)
        pattern_signal = detector.detect_pattern(technical_data)
        
        # Create trading signal
        from data_models import TradingSignal, TechnicalIndicators
        
        indicators = TechnicalIndicators(
            momentum=technical_data['momentum'],
            momentum_color=technical_data['momentum_color'],
            atr_value=technical_data['atr_value'],
            atr_signal=technical_data['atr_signal'],
            atr_confirmed=technical_data['atr_confirmed']
        )
        
        signal = TradingSignal(
            timestamp=data_point.timestamp,
            market_data=data_point,
            indicators=indicators,
            pattern=pattern_signal,
            position_status=None
        )
        
        # Process through trading engine
        position_status = engine.process_signal(signal)
        signal.position_status = position_status
        
        signals.append(signal)
    
    # Print results
    entry_signals = sum(1 for s in signals if s.pattern.entry_signal)
    exit_signals = sum(1 for s in signals if s.pattern.exit_signal)
    
    print(f"Entry Signals: {entry_signals}")
    print(f"Exit Signals: {exit_signals}")
    print(f"Total Trades: {len(engine.trades)}")
    
    if engine.trades:
        total_pnl = sum(trade.pnl for trade in engine.trades)
        winning_trades = sum(1 for trade in engine.trades if trade.pnl > 0)
        win_rate = winning_trades / len(engine.trades)
        
        print(f"Total P&L: ${total_pnl:.2f}")
        print(f"Win Rate: {win_rate:.2%}")
        print(f"Final Capital: ${engine.current_capital:.2f}")
    
    print("Integration test completed successfully!")


if __name__ == "__main__":
    # Run unit tests
    print("Running Unit Tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "=" * 50)
    
    # Run integration test
    run_integration_test()
