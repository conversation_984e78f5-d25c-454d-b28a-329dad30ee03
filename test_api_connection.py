"""
Test API connections with your credentials.
"""
from market_data_provider import MarketDataManager
from config import API_CONFIG


def test_api_connections():
    """Test both FMP and Alpaca API connections"""
    print("=" * 60)
    print("TESTING API CONNECTIONS WITH YOUR CREDENTIALS")
    print("=" * 60)
    
    # Initialize market data manager with your API keys
    data_manager = MarketDataManager(
        fmp_api_key=API_CONFIG.fmp_api_key,
        alpaca_api_key=API_CONFIG.alpaca_api_key,
        alpaca_secret=API_CONFIG.alpaca_secret_key
    )
    
    print(f"Using API Keys:")
    print(f"  Alpaca API Key: {API_CONFIG.alpaca_api_key}")
    print(f"  Alpaca Secret: {API_CONFIG.alpaca_secret_key[:10]}...")
    print(f"  FMP API Key: {API_CONFIG.fmp_api_key}")
    print(f"  Paper Trading: {API_CONFIG.alpaca_paper_trading}")
    
    # Test Alpaca account info
    print(f"\n" + "-" * 40)
    print("TESTING ALPACA CONNECTION")
    print("-" * 40)
    
    try:
        account_info = data_manager.alpaca_provider.get_account_info()
        if account_info:
            print("✅ Alpaca connection successful!")
            print(f"  Account ID: {account_info.get('id', 'N/A')}")
            print(f"  Account Status: {account_info.get('status', 'N/A')}")
            print(f"  Buying Power: ${float(account_info.get('buying_power', 0)):,.2f}")
            print(f"  Cash: ${float(account_info.get('cash', 0)):,.2f}")
            print(f"  Portfolio Value: ${float(account_info.get('portfolio_value', 0)):,.2f}")
        else:
            print("❌ Failed to get Alpaca account info")
    except Exception as e:
        print(f"❌ Alpaca connection error: {e}")
    
    # Test FMP connection
    print(f"\n" + "-" * 40)
    print("TESTING FMP CONNECTION")
    print("-" * 40)
    
    try:
        quote = data_manager.fmp_provider.get_real_time_quote("SPY")
        if quote:
            print("✅ FMP connection successful!")
            print(f"  Symbol: {quote['symbol']}")
            print(f"  Price: ${quote['price']:.2f}")
            print(f"  Change: {quote['change']:.2f} ({quote['changesPercentage']:.2f}%)")
        else:
            print("❌ Failed to get FMP quote")
    except Exception as e:
        print(f"❌ FMP connection error: {e}")
    
    # Test historical data retrieval
    print(f"\n" + "-" * 40)
    print("TESTING HISTORICAL DATA RETRIEVAL")
    print("-" * 40)
    
    try:
        # Test FMP historical data
        fmp_data = data_manager.get_market_data("SPY", source="fmp", timeframe="1min", days=1)
        print(f"✅ FMP historical data: {len(fmp_data)} bars retrieved")
        
        if fmp_data:
            latest = fmp_data[-1]
            print(f"  Latest bar: {latest.timestamp} - Close: ${latest.close:.2f}")
    except Exception as e:
        print(f"❌ FMP historical data error: {e}")
    
    try:
        # Test Alpaca historical data
        alpaca_data = data_manager.get_market_data("SPY", source="alpaca", timeframe="1Min", days=1)
        print(f"✅ Alpaca historical data: {len(alpaca_data)} bars retrieved")
        
        if alpaca_data:
            latest = alpaca_data[-1]
            print(f"  Latest bar: {latest.timestamp} - Close: ${latest.close:.2f}")
    except Exception as e:
        print(f"❌ Alpaca historical data error: {e}")
    
    # Test positions
    print(f"\n" + "-" * 40)
    print("TESTING CURRENT POSITIONS")
    print("-" * 40)
    
    try:
        positions = data_manager.alpaca_provider.get_positions()
        print(f"✅ Current positions retrieved: {len(positions)} positions")
        
        if positions:
            for pos in positions:
                symbol = pos.get('symbol', 'N/A')
                qty = pos.get('qty', '0')
                market_value = pos.get('market_value', '0')
                unrealized_pl = pos.get('unrealized_pl', '0')
                print(f"  {symbol}: {qty} shares, Value: ${float(market_value):,.2f}, P&L: ${float(unrealized_pl):,.2f}")
        else:
            print("  No current positions")
    except Exception as e:
        print(f"❌ Positions error: {e}")
    
    print(f"\n" + "=" * 60)
    print("API CONNECTION TEST COMPLETE")
    print("=" * 60)


if __name__ == "__main__":
    test_api_connections()
