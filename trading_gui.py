"""
Desktop GUI for Automated Momentum Reversal Trading System
"""
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
from datetime import datetime
from typing import List, Dict, Any
import queue

from stock_universe import StockUniverse
from mass_scanner import MassScanner, <PERSON>an<PERSON><PERSON><PERSON>
from automated_trading_system import AutomatedTradingSystem
from config import TRADING_CONFIG, API_CONFIG


class TradingGUI:
    """Main GUI application for the trading system"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Momentum Reversal Trading System - S&P 500 + $100B Market Cap Scanner")
        self.root.geometry("1400x900")
        self.root.configure(bg='#2b2b2b')
        
        # Threading and communication
        self.scan_thread = None
        self.trading_thread = None
        self.is_scanning = False
        self.is_trading = False
        self.message_queue = queue.Queue()
        
        # Data
        self.universe = []
        self.scan_results = []
        self.trading_system = None
        
        # Create GUI components
        self.create_widgets()
        self.setup_styles()
        
        # Start message processing
        self.process_messages()
        
        # Initialize universe
        self.load_universe()
    
    def setup_styles(self):
        """Setup custom styles for the GUI"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure colors
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#2b2b2b', foreground='white')
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'), background='#2b2b2b', foreground='#4CAF50')
        style.configure('Status.TLabel', font=('Arial', 10), background='#2b2b2b', foreground='#FFC107')
        style.configure('Success.TLabel', font=('Arial', 10, 'bold'), background='#2b2b2b', foreground='#4CAF50')
        style.configure('Error.TLabel', font=('Arial', 10, 'bold'), background='#2b2b2b', foreground='#F44336')
        
        # Configure buttons
        style.configure('Action.TButton', font=('Arial', 11, 'bold'))
        style.configure('Stop.TButton', font=('Arial', 11, 'bold'))
    
    def create_widgets(self):
        """Create all GUI widgets"""
        # Main title
        title_frame = tk.Frame(self.root, bg='#2b2b2b')
        title_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(title_frame, text="🎯 Momentum Reversal Trading System", 
                 style='Title.TLabel').pack(side='left')
        
        # Status indicators
        status_frame = tk.Frame(title_frame, bg='#2b2b2b')
        status_frame.pack(side='right')
        
        self.api_status = ttk.Label(status_frame, text="API: ✅ Connected", style='Success.TLabel')
        self.api_status.pack(side='right', padx=10)
        
        # Main container
        main_frame = tk.Frame(self.root, bg='#2b2b2b')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Left panel - Controls and Status
        left_panel = tk.Frame(main_frame, bg='#3b3b3b', relief='raised', bd=2)
        left_panel.pack(side='left', fill='y', padx=(0, 5))
        
        self.create_control_panel(left_panel)
        self.create_house_money_panel(left_panel)
        self.create_universe_panel(left_panel)
        
        # Right panel - Results and Logs
        right_panel = tk.Frame(main_frame, bg='#3b3b3b', relief='raised', bd=2)
        right_panel.pack(side='right', fill='both', expand=True, padx=(5, 0))
        
        self.create_results_panel(right_panel)
        self.create_log_panel(right_panel)
    
    def create_control_panel(self, parent):
        """Create control panel"""
        control_frame = tk.LabelFrame(parent, text="🎮 Trading Controls", 
                                     bg='#3b3b3b', fg='white', font=('Arial', 12, 'bold'))
        control_frame.pack(fill='x', padx=10, pady=10)
        
        # Scan controls
        scan_frame = tk.Frame(control_frame, bg='#3b3b3b')
        scan_frame.pack(fill='x', padx=10, pady=5)
        
        self.scan_button = ttk.Button(scan_frame, text="🔍 Start Mass Scan", 
                                     command=self.start_scan, style='Action.TButton')
        self.scan_button.pack(side='left', padx=5)
        
        self.stop_scan_button = ttk.Button(scan_frame, text="⏹ Stop Scan", 
                                          command=self.stop_scan, style='Stop.TButton', state='disabled')
        self.stop_scan_button.pack(side='left', padx=5)
        
        # Trading controls
        trading_frame = tk.Frame(control_frame, bg='#3b3b3b')
        trading_frame.pack(fill='x', padx=10, pady=5)
        
        self.trade_button = ttk.Button(trading_frame, text="💰 Start Trading", 
                                      command=self.start_trading, style='Action.TButton')
        self.trade_button.pack(side='left', padx=5)
        
        self.stop_trade_button = ttk.Button(trading_frame, text="⏹ Stop Trading", 
                                           command=self.stop_trading, style='Stop.TButton', state='disabled')
        self.stop_trade_button.pack(side='left', padx=5)
        
        # Status
        self.scan_status = ttk.Label(control_frame, text="Status: Ready", style='Status.TLabel')
        self.scan_status.pack(padx=10, pady=5)
    
    def create_house_money_panel(self, parent):
        """Create house money strategy panel"""
        hm_frame = tk.LabelFrame(parent, text="🏠 House Money Strategy", 
                                bg='#3b3b3b', fg='white', font=('Arial', 12, 'bold'))
        hm_frame.pack(fill='x', padx=10, pady=10)
        
        # Daily target progress
        progress_frame = tk.Frame(hm_frame, bg='#3b3b3b')
        progress_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(progress_frame, text="Daily Target:", bg='#3b3b3b', fg='white').pack(anchor='w')
        
        self.daily_pnl_var = tk.StringVar(value="$0.00 / $100.00 (0%)")
        self.daily_pnl_label = ttk.Label(progress_frame, textvariable=self.daily_pnl_var, 
                                        style='Header.TLabel')
        self.daily_pnl_label.pack(anchor='w')
        
        # Progress bar
        self.progress_bar = ttk.Progressbar(progress_frame, length=200, mode='determinate')
        self.progress_bar.pack(fill='x', pady=5)
        
        # Trading phase
        self.phase_var = tk.StringVar(value="Phase: INITIAL_CAPITAL")
        self.phase_label = ttk.Label(progress_frame, textvariable=self.phase_var, style='Status.TLabel')
        self.phase_label.pack(anchor='w')
        
        # Locked profit
        self.locked_profit_var = tk.StringVar(value="Locked Profit: $0.00")
        self.locked_profit_label = ttk.Label(progress_frame, textvariable=self.locked_profit_var, 
                                            style='Success.TLabel')
        self.locked_profit_label.pack(anchor='w')
    
    def create_universe_panel(self, parent):
        """Create stock universe panel"""
        universe_frame = tk.LabelFrame(parent, text="📊 Stock Universe", 
                                      bg='#3b3b3b', fg='white', font=('Arial', 12, 'bold'))
        universe_frame.pack(fill='x', padx=10, pady=10)
        
        self.universe_status = ttk.Label(universe_frame, text="Loading universe...", style='Status.TLabel')
        self.universe_status.pack(padx=10, pady=5)
        
        # Quick stats
        stats_frame = tk.Frame(universe_frame, bg='#3b3b3b')
        stats_frame.pack(fill='x', padx=10, pady=5)
        
        self.total_stocks_var = tk.StringVar(value="Total Stocks: 0")
        ttk.Label(stats_frame, textvariable=self.total_stocks_var, bg='#3b3b3b', fg='white').pack(anchor='w')
        
        self.scan_progress_var = tk.StringVar(value="Scanned: 0/0")
        ttk.Label(stats_frame, textvariable=self.scan_progress_var, bg='#3b3b3b', fg='white').pack(anchor='w')
    
    def create_results_panel(self, parent):
        """Create results panel"""
        results_frame = tk.LabelFrame(parent, text="🎯 Scan Results & Trading Opportunities", 
                                     bg='#3b3b3b', fg='white', font=('Arial', 12, 'bold'))
        results_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Results tree
        columns = ('Symbol', 'Entry Signals', 'Momentum Reversals', 'ATR Confirmations', 'Pattern Strength', 'Price')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=120, anchor='center')
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(results_frame, orient='vertical', command=self.results_tree.yview)
        h_scrollbar = ttk.Scrollbar(results_frame, orient='horizontal', command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack tree and scrollbars
        self.results_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')
        
        # Bind double-click
        self.results_tree.bind('<Double-1>', self.on_stock_double_click)
    
    def create_log_panel(self, parent):
        """Create log panel"""
        log_frame = tk.LabelFrame(parent, text="📝 System Log", 
                                 bg='#3b3b3b', fg='white', font=('Arial', 12, 'bold'))
        log_frame.pack(fill='x', padx=10, pady=(0, 10))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, bg='#1e1e1e', fg='#00ff00', 
                                                 font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Add initial log message
        self.log_message("System initialized with your API keys")
        self.log_message(f"Daily target: ${TRADING_CONFIG.daily_profit_target}")
        self.log_message(f"Initial profit lock: ${TRADING_CONFIG.initial_profit_lock}")
    
    def log_message(self, message: str):
        """Add message to log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # Keep log size manageable
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.log_text.delete("1.0", "10.0")
    
    def load_universe(self):
        """Load stock universe in background"""
        def load_task():
            try:
                self.message_queue.put(("log", "Loading S&P 500 + $100B market cap stocks..."))
                universe_manager = StockUniverse()
                self.universe = universe_manager.build_universe()
                
                self.message_queue.put(("universe_loaded", len(self.universe)))
                self.message_queue.put(("log", f"Loaded {len(self.universe)} stocks for scanning"))
                
            except Exception as e:
                self.message_queue.put(("error", f"Error loading universe: {e}"))
        
        threading.Thread(target=load_task, daemon=True).start()
    
    def start_scan(self):
        """Start mass scanning"""
        if self.is_scanning:
            return
        
        if not self.universe:
            messagebox.showwarning("Warning", "Stock universe not loaded yet. Please wait.")
            return
        
        self.is_scanning = True
        self.scan_button.config(state='disabled')
        self.stop_scan_button.config(state='normal')
        self.scan_status.config(text="Status: Scanning...")
        
        def scan_task():
            try:
                self.message_queue.put(("log", f"Starting mass scan of {len(self.universe)} stocks..."))
                
                scanner = MassScanner(max_workers=8)
                
                # Scan in batches with progress updates
                batches = [self.universe[i:i+25] for i in range(0, len(self.universe), 25)]
                all_results = []
                
                for i, batch in enumerate(batches):
                    if not self.is_scanning:  # Check if stopped
                        break
                    
                    self.message_queue.put(("scan_progress", (i * 25, len(self.universe))))
                    self.message_queue.put(("log", f"Scanning batch {i+1}/{len(batches)}..."))
                    
                    batch_results = scanner.scan_batch(batch)
                    all_results.extend(batch_results)
                    
                    # Update results in real-time
                    self.message_queue.put(("scan_results", all_results.copy()))
                    
                    time.sleep(1)  # Brief pause between batches
                
                if self.is_scanning:
                    self.message_queue.put(("scan_complete", all_results))
                    
            except Exception as e:
                self.message_queue.put(("error", f"Scan error: {e}"))
            finally:
                self.message_queue.put(("scan_finished", None))
        
        self.scan_thread = threading.Thread(target=scan_task, daemon=True)
        self.scan_thread.start()
    
    def stop_scan(self):
        """Stop scanning"""
        self.is_scanning = False
        self.scan_button.config(state='normal')
        self.stop_scan_button.config(state='disabled')
        self.scan_status.config(text="Status: Stopped")
        self.log_message("Scan stopped by user")
    
    def start_trading(self):
        """Start automated trading"""
        if not self.scan_results:
            messagebox.showwarning("Warning", "No scan results available. Please run a scan first.")
            return
        
        # Get stocks with entry signals
        entry_stocks = [r for r in self.scan_results if r.success and r.entry_signals > 0]
        
        if not entry_stocks:
            messagebox.showinfo("Info", "No entry signals found. The system will wait for opportunities.")
        
        self.is_trading = True
        self.trade_button.config(state='disabled')
        self.stop_trade_button.config(state='normal')
        
        self.log_message("🚀 Starting automated trading with house money strategy")
        
        # Initialize trading system
        try:
            self.trading_system = AutomatedTradingSystem()
            self.log_message("Trading system initialized successfully")
            
            # Start trading thread
            def trading_task():
                symbols = [r.symbol for r in entry_stocks[:5]]  # Trade top 5 opportunities
                if symbols:
                    self.log_message(f"Trading symbols: {', '.join(symbols)}")
                    self.trading_system.run_live_trading(symbols)
                else:
                    self.log_message("No symbols to trade - monitoring for opportunities")
            
            self.trading_thread = threading.Thread(target=trading_task, daemon=True)
            self.trading_thread.start()
            
        except Exception as e:
            self.log_message(f"Error starting trading: {e}")
            self.stop_trading()
    
    def stop_trading(self):
        """Stop automated trading"""
        self.is_trading = False
        self.trade_button.config(state='normal')
        self.stop_trade_button.config(state='disabled')
        
        if self.trading_system:
            self.trading_system.stop_trading()
        
        self.log_message("🛑 Trading stopped")
    
    def update_results_display(self, results: List[ScanResult]):
        """Update results tree with scan results"""
        # Clear existing results
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # Sort by entry signals, then pattern strength
        sorted_results = sorted(
            [r for r in results if r.success],
            key=lambda x: (x.entry_signals, x.pattern_strength, x.momentum_reversals),
            reverse=True
        )
        
        # Add top results
        for result in sorted_results[:50]:  # Show top 50
            # Color code based on signals
            if result.entry_signals > 0:
                tags = ('entry_signal',)
            elif result.momentum_reversals > 0:
                tags = ('momentum_reversal',)
            else:
                tags = ('normal',)
            
            self.results_tree.insert('', 'end', values=(
                result.symbol,
                result.entry_signals,
                result.momentum_reversals,
                result.atr_confirmations,
                f"{result.pattern_strength:.2f}",
                f"${result.current_price:.2f}"
            ), tags=tags)
        
        # Configure tags
        self.results_tree.tag_configure('entry_signal', background='#4CAF50', foreground='white')
        self.results_tree.tag_configure('momentum_reversal', background='#FFC107', foreground='black')
        self.results_tree.tag_configure('normal', background='white', foreground='black')
    
    def on_stock_double_click(self, event):
        """Handle double-click on stock"""
        selection = self.results_tree.selection()
        if selection:
            item = self.results_tree.item(selection[0])
            symbol = item['values'][0]
            messagebox.showinfo("Stock Details", f"Selected: {symbol}\nDouble-click functionality can be expanded for detailed analysis.")
    
    def process_messages(self):
        """Process messages from background threads"""
        try:
            while True:
                message_type, data = self.message_queue.get_nowait()
                
                if message_type == "log":
                    self.log_message(data)
                
                elif message_type == "universe_loaded":
                    self.total_stocks_var.set(f"Total Stocks: {data}")
                    self.universe_status.config(text=f"✅ {data} stocks loaded")
                
                elif message_type == "scan_progress":
                    scanned, total = data
                    self.scan_progress_var.set(f"Scanned: {scanned}/{total}")
                
                elif message_type == "scan_results":
                    self.scan_results = data
                    self.update_results_display(data)
                    
                    # Count opportunities
                    entry_signals = sum(1 for r in data if r.success and r.entry_signals > 0)
                    if entry_signals > 0:
                        self.log_message(f"🎯 Found {entry_signals} stocks with entry signals!")
                
                elif message_type == "scan_complete":
                    self.scan_results = data
                    self.update_results_display(data)
                    self.log_message("✅ Mass scan completed!")
                
                elif message_type == "scan_finished":
                    self.is_scanning = False
                    self.scan_button.config(state='normal')
                    self.stop_scan_button.config(state='disabled')
                    self.scan_status.config(text="Status: Ready")
                
                elif message_type == "error":
                    self.log_message(f"❌ {data}")
                    messagebox.showerror("Error", data)
                
        except queue.Empty:
            pass
        
        # Schedule next check
        self.root.after(100, self.process_messages)
    
    def run(self):
        """Start the GUI application"""
        self.root.mainloop()


def main():
    """Main entry point"""
    app = TradingGUI()
    app.run()


if __name__ == "__main__":
    main()
