"""
Pattern detection engine for momentum reversal patterns.
"""
from typing import List, Dict, Any
from data_models import MomentumColor, ATRSignal, PatternSignal
from collections import deque


class MomentumReversalDetector:
    """Detects momentum reversal patterns based on specific criteria"""
    
    def __init__(self, min_declining_bars: int = 4):
        self.min_declining_bars = min_declining_bars
        self.momentum_history: deque = deque(maxlen=20)
        self.color_history: deque = deque(maxlen=20)
        self.declining_momentum_history: deque = deque(maxlen=20)
        
    def update_history(self, momentum: float, color: MomentumColor, is_declining: bool):
        """Update historical data for pattern detection"""
        self.momentum_history.append(momentum)
        self.color_history.append(color)
        self.declining_momentum_history.append(is_declining)
    
    def detect_declining_red_momentum_phase(self) -> bool:
        """
        Detect 4+ consecutive bars where:
        - Each bar has declining momentum
        - All bars are colored red
        """
        if len(self.color_history) < self.min_declining_bars:
            return False
        
        # Check last N bars for declining red momentum
        consecutive_count = 0
        
        for i in range(len(self.color_history) - 1, -1, -1):
            if (self.color_history[i] == MomentumColor.RED and 
                self.declining_momentum_history[i]):
                consecutive_count += 1
            else:
                break
        
        return consecutive_count >= self.min_declining_bars
    
    def detect_momentum_reversal_signal(self) -> bool:
        """
        Detect momentum reversal:
        - Current bar turns yellow (positive momentum)
        - Previous bar was red (negative momentum)
        """
        if len(self.color_history) < 2:
            return False
        
        current_color = self.color_history[-1]
        previous_color = self.color_history[-2]
        
        return (current_color == MomentumColor.YELLOW and 
                previous_color == MomentumColor.RED)
    
    def validate_atr_confirmation(self, atr_signal: ATRSignal, atr_confirmed: bool) -> bool:
        """
        Validate ATR confirmation:
        - ATR confirmation must be true
        - ATR signal should be blue (uptrend)
        """
        return atr_confirmed and atr_signal == ATRSignal.BLUE
    
    def detect_pattern(self, technical_data: Dict[str, Any]) -> PatternSignal:
        """
        Main pattern detection logic combining all criteria
        """
        momentum = technical_data['momentum']
        momentum_color = technical_data['momentum_color']
        is_declining = technical_data['is_declining_momentum']
        atr_signal = technical_data['atr_signal']
        atr_confirmed = technical_data['atr_confirmed']
        
        # Update historical data
        self.update_history(momentum, momentum_color, is_declining)
        
        # Detect individual components
        declining_red_momentum = self.detect_declining_red_momentum_phase()
        momentum_reversal = self.detect_momentum_reversal_signal()
        atr_confirmation = self.validate_atr_confirmation(atr_signal, atr_confirmed)
        
        # Bar pattern detected when declining red momentum was present
        bar_pattern_detected = declining_red_momentum
        
        # Entry signal when all three conditions are met
        entry_signal = (declining_red_momentum and 
                       momentum_reversal and 
                       atr_confirmation)
        
        # Exit signal when both bar pattern AND ATR confirmation deteriorate
        exit_signal = self.should_exit(atr_signal, atr_confirmed)
        
        return PatternSignal(
            declining_red_momentum=declining_red_momentum,
            momentum_reversal=momentum_reversal,
            atr_confirmation=atr_confirmation,
            bar_pattern_detected=bar_pattern_detected,
            entry_signal=entry_signal,
            exit_signal=exit_signal
        )
    
    def should_exit(self, atr_signal: ATRSignal, atr_confirmed: bool) -> bool:
        """
        Determine if position should be exited:
        - Both bar pattern AND ATR confirmation must deteriorate
        """
        # Check if ATR confirmation has deteriorated
        atr_deteriorated = not atr_confirmed or atr_signal != ATRSignal.BLUE
        
        # Check if bar pattern has deteriorated (no recent declining red momentum)
        bar_pattern_deteriorated = not self.has_recent_favorable_pattern()
        
        return atr_deteriorated and bar_pattern_deteriorated
    
    def has_recent_favorable_pattern(self) -> bool:
        """
        Check if recent bars still show favorable pattern characteristics
        """
        if len(self.color_history) < 2:
            return False
        
        # Consider pattern favorable if:
        # 1. Current bar is not red with declining momentum, OR
        # 2. We still have some yellow/green momentum
        current_color = self.color_history[-1]
        current_declining = self.declining_momentum_history[-1]
        
        # Pattern is still favorable if current bar is not declining red
        if current_color != MomentumColor.RED or not current_declining:
            return True
        
        # Also check if we have any non-red bars in recent history
        recent_bars = 3
        if len(self.color_history) >= recent_bars:
            recent_colors = list(self.color_history)[-recent_bars:]
            return any(color != MomentumColor.RED for color in recent_colors)
        
        return False
    
    def get_pattern_strength(self) -> float:
        """
        Calculate pattern strength score (0.0 to 1.0)
        """
        if len(self.momentum_history) < self.min_declining_bars:
            return 0.0
        
        strength_factors = []
        
        # Factor 1: Length of declining red momentum phase
        consecutive_red_declining = 0
        for i in range(len(self.color_history) - 1, -1, -1):
            if (self.color_history[i] == MomentumColor.RED and 
                self.declining_momentum_history[i]):
                consecutive_red_declining += 1
            else:
                break
        
        length_strength = min(consecutive_red_declining / 8.0, 1.0)  # Max at 8 bars
        strength_factors.append(length_strength)
        
        # Factor 2: Momentum magnitude (how oversold)
        if self.momentum_history:
            recent_momentum = list(self.momentum_history)[-min(5, len(self.momentum_history)):]
            avg_momentum = sum(recent_momentum) / len(recent_momentum)
            magnitude_strength = min(abs(avg_momentum) / 10.0, 1.0)  # Max at -10%
            strength_factors.append(magnitude_strength)
        
        # Factor 3: Consistency of decline
        if len(self.momentum_history) >= 3:
            recent_momentum = list(self.momentum_history)[-3:]
            is_consistent = all(recent_momentum[i] < recent_momentum[i-1] 
                              for i in range(1, len(recent_momentum)))
            consistency_strength = 1.0 if is_consistent else 0.5
            strength_factors.append(consistency_strength)
        
        return sum(strength_factors) / len(strength_factors) if strength_factors else 0.0
