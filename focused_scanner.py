"""
Focused scanner for testing with high-volume stocks first.
"""
from mass_scanner import MassScanner
from stock_universe import StockUniverse


def run_focused_scan():
    """Run a focused scan on high-volume stocks first"""
    print("=" * 80)
    print("FOCUSED MOMENTUM REVERSAL SCANNER")
    print("Testing with High-Volume Stocks First")
    print("=" * 80)
    
    # Get high-volume stocks for testing
    universe_manager = StockUniverse()
    high_volume_stocks = universe_manager.get_high_volume_stocks()
    
    # Add some major stocks
    focus_stocks = [
        # Major ETFs
        "SPY", "QQQ", "IWM",
        
        # Mega caps
        "AAPL", "MSFT", "GOOGL", "AMZN", "NVDA", "META", "TSLA",
        
        # Large caps with volatility
        "AMD", "NFLX", "BABA", "PLTR", "SOFI", "F", "BAC", "T",
        
        # Volatile stocks
        "GME", "AMC", "BBBY", "COIN", "HOOD", "RIVN", "LCID",
        
        # Energy
        "XOM", "CVX", "COP", "SLB",
        
        # Finance
        "JPM", "WFC", "GS", "MS",
        
        # Tech
        "INTC", "IBM", "ORCL", "CRM"
    ]
    
    print(f"Scanning {len(focus_stocks)} high-volume stocks...")
    print(f"Stocks: {', '.join(focus_stocks[:10])}...")
    
    # Initialize scanner with fewer workers for testing
    scanner = MassScanner(max_workers=5)
    
    # Run the scan
    results = scanner.scan_batch(focus_stocks)
    
    # Generate report
    report = scanner.generate_scan_report(results)
    
    # Display results
    print(f"\n" + "=" * 80)
    print("FOCUSED SCAN RESULTS")
    print("=" * 80)
    
    summary = report["scan_summary"]
    patterns = report["pattern_detection"]
    
    print(f"Scan Summary:")
    print(f"  Total Stocks Scanned: {summary['total_stocks']}")
    print(f"  Successful Scans: {summary['successful_scans']}")
    print(f"  Failed Scans: {summary['failed_scans']}")
    print(f"  Success Rate: {summary['success_rate']:.1f}%")
    
    print(f"\nPattern Detection Results:")
    print(f"  🎯 Stocks with Entry Signals: {patterns['stocks_with_entry_signals']}")
    print(f"  📉 Declining Momentum Detected: {patterns['stocks_with_declining_momentum']}")
    print(f"  🔄 Momentum Reversals Found: {patterns['stocks_with_momentum_reversals']}")
    print(f"  ✅ ATR Confirmations: {patterns['stocks_with_atr_confirmation']}")
    
    # Show detailed results for stocks with signals
    entry_signal_stocks = [r for r in results if r.success and r.entry_signals > 0]
    
    if entry_signal_stocks:
        print(f"\n🏆 STOCKS WITH ENTRY SIGNALS:")
        print("-" * 80)
        print(f"{'Symbol':<8} {'Entry':<6} {'Decline':<8} {'Reversal':<9} {'ATR':<6} {'Strength':<9} {'Price':<10}")
        print("-" * 80)
        
        for stock in entry_signal_stocks:
            print(f"{stock.symbol:<8} {stock.entry_signals:<6} "
                  f"{stock.declining_red_momentum:<8} {stock.momentum_reversals:<9} "
                  f"{stock.atr_confirmations:<6} {stock.pattern_strength:<9.2f} "
                  f"${stock.current_price:<9.2f}")
    
    # Show stocks with momentum reversals (potential setups)
    reversal_stocks = [r for r in results if r.success and r.momentum_reversals > 0 and r.entry_signals == 0]
    
    if reversal_stocks:
        print(f"\n📈 STOCKS WITH MOMENTUM REVERSALS (Potential Setups):")
        print("-" * 80)
        print(f"{'Symbol':<8} {'Reversals':<9} {'ATR Conf':<8} {'Strength':<9} {'Price':<10}")
        print("-" * 80)
        
        for stock in sorted(reversal_stocks, key=lambda x: x.momentum_reversals, reverse=True)[:10]:
            print(f"{stock.symbol:<8} {stock.momentum_reversals:<9} "
                  f"{stock.atr_confirmations:<8} {stock.pattern_strength:<9.2f} "
                  f"${stock.current_price:<9.2f}")
    
    # Show stocks with declining momentum (building patterns)
    declining_stocks = [r for r in results if r.success and r.declining_red_momentum > 0]
    
    if declining_stocks:
        print(f"\n📉 STOCKS WITH DECLINING MOMENTUM (Building Patterns):")
        print("-" * 80)
        print(f"{'Symbol':<8} {'Declining':<9} {'Reversals':<9} {'Price':<10}")
        print("-" * 80)
        
        for stock in sorted(declining_stocks, key=lambda x: x.declining_red_momentum, reverse=True)[:10]:
            print(f"{stock.symbol:<8} {stock.declining_red_momentum:<9} "
                  f"{stock.momentum_reversals:<9} ${stock.current_price:<9.2f}")
    
    # Show any errors
    failed_stocks = [r for r in results if not r.success]
    if failed_stocks:
        print(f"\n❌ FAILED SCANS ({len(failed_stocks)} stocks):")
        print("-" * 50)
        error_summary = {}
        for stock in failed_stocks:
            error = stock.error_message
            if error not in error_summary:
                error_summary[error] = []
            error_summary[error].append(stock.symbol)
        
        for error, symbols in error_summary.items():
            print(f"  {error}: {', '.join(symbols[:5])}")
            if len(symbols) > 5:
                print(f"    ... and {len(symbols) - 5} more")
    
    print(f"\n" + "=" * 80)
    print("FOCUSED SCAN COMPLETE!")
    print("=" * 80)
    
    if entry_signal_stocks:
        print(f"🎯 Found {len(entry_signal_stocks)} stocks with entry signals!")
        print(f"Ready to execute trades on: {', '.join([s.symbol for s in entry_signal_stocks[:5]])}")
    else:
        print(f"📊 No entry signals found - system is waiting for high-probability setups")
        print(f"This is normal market behavior - the system is selective")
    
    if reversal_stocks:
        print(f"📈 {len(reversal_stocks)} stocks showing momentum reversals (watch list)")
    
    if declining_stocks:
        print(f"📉 {len(declining_stocks)} stocks building declining momentum patterns")
    
    return results


if __name__ == "__main__":
    run_focused_scan()
