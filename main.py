"""
Main execution script for the automated trading system.
"""
import argparse
import sys
import json
from datetime import datetime
from automated_trading_system import AutomatedTradingSystem
from config import TRADING_CONFIG, validate_config


def run_backtest(system: AutomatedTradingSystem, symbol: str):
    """Run backtest for a single symbol"""
    print(f"\n{'='*60}")
    print(f"RUNNING BACKTEST FOR {symbol}")
    print(f"{'='*60}")
    
    results = system.run_backtest(symbol)
    
    if "error" in results:
        print(f"Error: {results['error']}")
        return
    
    # Display results
    print(f"\nBacktest Results for {symbol}:")
    print(f"Period: {results['backtest_period']['start']} to {results['backtest_period']['end']}")
    print(f"Total Bars Processed: {results['backtest_period']['total_bars']}")
    
    print(f"\nSignal Statistics:")
    stats = results['signal_statistics']
    print(f"  Entry Signals: {stats['entry_signals']}")
    print(f"  Exit Signals: {stats['exit_signals']}")
    print(f"  Declining Red Momentum Detected: {stats['declining_red_momentum_detected']}")
    print(f"  Momentum Reversals Detected: {stats['momentum_reversals_detected']}")
    print(f"  ATR Confirmations: {stats['atr_confirmations']}")
    
    print(f"\nTrading Performance:")
    perf = results['trading_performance']
    if 'message' not in perf:
        print(f"  Total Trades: {perf['total_trades']}")
        print(f"  Win Rate: {perf['win_rate']}")
        print(f"  Total P&L: {perf['total_pnl']}")
        print(f"  Total Return: {perf['total_return']}")
        print(f"  Average Win: {perf['avg_win']}")
        print(f"  Average Loss: {perf['avg_loss']}")
        print(f"  Final Capital: {perf['current_capital']}")
    else:
        print(f"  {perf['message']}")
    
    print(f"\nPattern Success Rate:")
    pattern_success = results['pattern_success_rate']
    print(f"  Success Rate: {pattern_success['success_rate']:.2%}")
    print(f"  Successful Patterns: {pattern_success['successful_patterns']}")
    print(f"  Total Patterns: {pattern_success['total_patterns']}")
    
    # Export detailed results
    filename = system.export_results(f"results/backtest_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    print(f"\nDetailed results exported to: {filename}")


def run_multiple_backtests(system: AutomatedTradingSystem, symbols: list):
    """Run backtests for multiple symbols"""
    print(f"\n{'='*60}")
    print(f"RUNNING BACKTESTS FOR {len(symbols)} SYMBOLS")
    print(f"{'='*60}")
    
    all_results = {}
    
    for symbol in symbols:
        print(f"\nProcessing {symbol}...")
        results = system.run_backtest(symbol)
        all_results[symbol] = results
        
        if "error" not in results:
            perf = results['trading_performance']
            if 'message' not in perf:
                print(f"  {symbol}: {perf['total_trades']} trades, {perf['win_rate']} win rate, {perf['total_return']} return")
            else:
                print(f"  {symbol}: No trades executed")
        else:
            print(f"  {symbol}: Error - {results['error']}")
    
    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    
    total_trades = 0
    total_winners = 0
    total_pnl = 0.0
    
    for symbol, results in all_results.items():
        if "error" not in results:
            perf = results['trading_performance']
            if 'message' not in perf:
                trades = int(perf['total_trades'])
                win_rate = float(perf['win_rate'].rstrip('%')) / 100
                pnl = float(perf['total_pnl'].replace('$', '').replace(',', ''))
                
                total_trades += trades
                total_winners += int(trades * win_rate)
                total_pnl += pnl
    
    if total_trades > 0:
        overall_win_rate = total_winners / total_trades
        print(f"Overall Statistics:")
        print(f"  Total Trades: {total_trades}")
        print(f"  Overall Win Rate: {overall_win_rate:.2%}")
        print(f"  Total P&L: ${total_pnl:.2f}")
        print(f"  Average P&L per Symbol: ${total_pnl / len(symbols):.2f}")
    
    # Export combined results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"results/multi_backtest_{timestamp}.json"
    with open(filename, 'w') as f:
        json.dump(all_results, f, indent=2, default=str)
    
    print(f"\nCombined results exported to: {filename}")


def run_live_trading(system: AutomatedTradingSystem, symbols: list):
    """Run live trading"""
    print(f"\n{'='*60}")
    print(f"STARTING LIVE TRADING")
    print(f"{'='*60}")
    print(f"Symbols: {', '.join(symbols)}")
    print(f"Update Interval: {system.market_data_manager.alpaca_provider.base_url}")
    print("Press Ctrl+C to stop...")
    
    try:
        system.run_live_trading(symbols)
    except KeyboardInterrupt:
        print("\nLive trading stopped by user")
    finally:
        # Export final results
        filename = system.export_results()
        print(f"Final results exported to: {filename}")


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description="Automated Momentum Reversal Trading System")
    parser.add_argument("--mode", choices=["backtest", "live", "multi-backtest"], 
                       default="backtest", help="Execution mode")
    parser.add_argument("--symbol", type=str, default="SPY", 
                       help="Symbol to trade (for single backtest)")
    parser.add_argument("--symbols", type=str, nargs="+", 
                       help="Multiple symbols to trade")
    parser.add_argument("--config", type=str, help="Configuration file path")
    
    args = parser.parse_args()
    
    # Validate configuration
    if not validate_config():
        print("Configuration validation failed. Please check your settings.")
        sys.exit(1)
    
    # Initialize trading system
    try:
        system = AutomatedTradingSystem()
        print("Automated Trading System initialized successfully")
        
        # Display configuration
        print(f"\nConfiguration:")
        print(f"  Initial Capital: ${TRADING_CONFIG.initial_capital:,.2f}")
        print(f"  Position Size: {TRADING_CONFIG.position_size_percent:.1%}")
        print(f"  Momentum Period: {TRADING_CONFIG.momentum_period}")
        print(f"  ATR Period: {TRADING_CONFIG.atr_period}")
        print(f"  Min Declining Bars: {TRADING_CONFIG.min_declining_bars}")
        print(f"  Paper Trading: {system.market_data_manager.alpaca_provider.paper_trading}")
        
    except Exception as e:
        print(f"Error initializing trading system: {e}")
        sys.exit(1)
    
    # Determine symbols to use
    if args.symbols:
        symbols = args.symbols
    elif args.mode == "multi-backtest":
        symbols = TRADING_CONFIG.symbols
    else:
        symbols = [args.symbol]
    
    # Execute based on mode
    try:
        if args.mode == "backtest":
            run_backtest(system, symbols[0])
        elif args.mode == "multi-backtest":
            run_multiple_backtests(system, symbols)
        elif args.mode == "live":
            run_live_trading(system, symbols)
            
    except Exception as e:
        print(f"Error during execution: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
