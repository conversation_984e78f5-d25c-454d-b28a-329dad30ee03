"""
Main automated trading system that orchestrates all components.
"""
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd
import json

from data_models import (
    MarketData, TechnicalIndicators, TradingSignal, 
    PositionStatus, MomentumColor, ATRSignal
)
from technical_indicators import TechnicalAnalyzer
from pattern_detector import MomentumReversalDetector
from trading_engine import TradingEngine
from market_data_provider import MarketDataManager
from config import API_CONFIG, TRADING_CONFIG, SYSTEM_CONFIG, validate_config


class AutomatedTradingSystem:
    """Main automated trading system"""
    
    def __init__(self):
        # Validate configuration
        if not validate_config():
            raise ValueError("Invalid configuration")
        
        # Initialize components
        self.market_data_manager = MarketDataManager(
            fmp_api_key=API_CONFIG.fmp_api_key,
            alpaca_api_key=API_CONFIG.alpaca_api_key,
            alpaca_secret=API_CONFIG.alpaca_secret_key
        )
        
        self.technical_analyzer = TechnicalAnalyzer(
            momentum_period=TRADING_CONFIG.momentum_period,
            atr_period=TRADING_CONFIG.atr_period,
            atr_multiplier=TRADING_CONFIG.atr_multiplier
        )
        
        self.pattern_detector = MomentumReversalDetector(
            min_declining_bars=TRADING_CONFIG.min_declining_bars
        )
        
        self.trading_engine = TradingEngine(
            initial_capital=TRADING_CONFIG.initial_capital,
            position_size=TRADING_CONFIG.position_size_percent
        )
        
        # System state
        self.is_running = False
        self.current_symbol = None
        self.trading_signals: List[TradingSignal] = []
        self.performance_history: List[Dict[str, Any]] = []
        
        # Setup logging
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("Automated Trading System initialized")
    
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=getattr(logging, SYSTEM_CONFIG.log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(SYSTEM_CONFIG.log_file),
                logging.StreamHandler()
            ]
        )
    
    def process_market_data(self, symbol: str, market_data: MarketData) -> Optional[TradingSignal]:
        """Process single market data point and generate trading signal"""
        try:
            # Perform technical analysis
            technical_data = self.technical_analyzer.analyze(market_data)
            
            # Create technical indicators object
            indicators = TechnicalIndicators(
                momentum=technical_data['momentum'],
                momentum_color=technical_data['momentum_color'],
                atr_value=technical_data['atr_value'],
                atr_signal=technical_data['atr_signal'],
                atr_confirmed=technical_data['atr_confirmed']
            )
            
            # Detect patterns
            pattern_signal = self.pattern_detector.detect_pattern(technical_data)
            
            # Create trading signal
            trading_signal = TradingSignal(
                timestamp=market_data.timestamp,
                market_data=market_data,
                indicators=indicators,
                pattern=pattern_signal,
                position_status=PositionStatus.OUT  # Will be updated by trading engine
            )
            
            # Process signal through trading engine
            position_status = self.trading_engine.process_signal(trading_signal)
            trading_signal.position_status = position_status
            
            # Set entry/exit prices if applicable
            if pattern_signal.entry_signal and position_status == PositionStatus.IN:
                trading_signal.entry_price = market_data.close
            elif pattern_signal.exit_signal and position_status == PositionStatus.OUT:
                trading_signal.exit_price = market_data.close
            
            return trading_signal
            
        except Exception as e:
            self.logger.error(f"Error processing market data: {e}")
            return None
    
    def run_backtest(self, symbol: str, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """Run backtest on historical data"""
        self.logger.info(f"Starting backtest for {symbol}")
        
        # Reset trading engine
        self.trading_engine.reset()
        
        # Get historical data
        market_data = self.market_data_manager.get_market_data(
            symbol=symbol,
            source="fmp",
            timeframe=TRADING_CONFIG.data_timeframe,
            days=TRADING_CONFIG.lookback_days
        )
        
        if not market_data:
            self.logger.error(f"No market data available for {symbol}")
            return {"error": "No market data available"}
        
        self.logger.info(f"Processing {len(market_data)} data points")
        
        # Process each data point
        signals = []
        for data_point in market_data:
            signal = self.process_market_data(symbol, data_point)
            if signal:
                signals.append(signal)
        
        # Store signals
        self.trading_signals = signals
        
        # Generate results
        results = self.generate_backtest_results(symbol, signals)
        
        self.logger.info(f"Backtest completed for {symbol}")
        return results
    
    def generate_backtest_results(self, symbol: str, signals: List[TradingSignal]) -> Dict[str, Any]:
        """Generate comprehensive backtest results"""
        if not signals:
            return {"error": "No signals generated"}
        
        # Basic statistics
        total_signals = len(signals)
        entry_signals = sum(1 for s in signals if s.pattern.entry_signal)
        exit_signals = sum(1 for s in signals if s.pattern.exit_signal)
        
        # Pattern statistics
        declining_red_momentum_count = sum(1 for s in signals if s.pattern.declining_red_momentum)
        momentum_reversal_count = sum(1 for s in signals if s.pattern.momentum_reversal)
        atr_confirmation_count = sum(1 for s in signals if s.pattern.atr_confirmation)
        
        # Trading performance
        performance_summary = self.trading_engine.get_performance_summary()
        
        # Signal analysis
        signal_analysis = self.analyze_signals(signals)
        
        results = {
            "symbol": symbol,
            "backtest_period": {
                "start": signals[0].timestamp.isoformat(),
                "end": signals[-1].timestamp.isoformat(),
                "total_bars": total_signals
            },
            "signal_statistics": {
                "total_signals": total_signals,
                "entry_signals": entry_signals,
                "exit_signals": exit_signals,
                "declining_red_momentum_detected": declining_red_momentum_count,
                "momentum_reversals_detected": momentum_reversal_count,
                "atr_confirmations": atr_confirmation_count
            },
            "trading_performance": performance_summary,
            "signal_analysis": signal_analysis,
            "pattern_success_rate": self.calculate_pattern_success_rate(signals)
        }
        
        return results
    
    def analyze_signals(self, signals: List[TradingSignal]) -> Dict[str, Any]:
        """Analyze signal patterns and characteristics"""
        if not signals:
            return {}
        
        # Momentum analysis
        momentum_values = [s.indicators.momentum for s in signals]
        momentum_colors = [s.indicators.momentum_color.value for s in signals]
        
        # ATR analysis
        atr_values = [s.indicators.atr_value for s in signals]
        atr_signals = [s.indicators.atr_signal.value for s in signals]
        
        return {
            "momentum_stats": {
                "min": min(momentum_values),
                "max": max(momentum_values),
                "mean": sum(momentum_values) / len(momentum_values),
                "red_bars": momentum_colors.count("red"),
                "yellow_bars": momentum_colors.count("yellow"),
                "green_bars": momentum_colors.count("green")
            },
            "atr_stats": {
                "min_atr": min(atr_values),
                "max_atr": max(atr_values),
                "mean_atr": sum(atr_values) / len(atr_values),
                "blue_signals": atr_signals.count("blue"),
                "red_signals": atr_signals.count("red"),
                "neutral_signals": atr_signals.count("neutral")
            }
        }
    
    def calculate_pattern_success_rate(self, signals: List[TradingSignal]) -> Dict[str, float]:
        """Calculate success rate of pattern detection"""
        trades = self.trading_engine.trades

        if not trades:
            return {
                "success_rate": 0.0,
                "successful_patterns": 0,
                "total_patterns": 0
            }

        successful_trades = sum(1 for trade in trades if trade.pnl > 0)
        total_patterns = len(trades)

        return {
            "success_rate": successful_trades / total_patterns if total_patterns > 0 else 0.0,
            "successful_patterns": successful_trades,
            "total_patterns": total_patterns
        }
    
    def run_live_trading(self, symbols: List[str] = None):
        """Run live trading system"""
        if symbols is None:
            symbols = TRADING_CONFIG.symbols
        
        self.logger.info(f"Starting live trading for symbols: {symbols}")
        self.is_running = True
        
        try:
            while self.is_running:
                for symbol in symbols:
                    self.process_symbol_live(symbol)
                
                # Wait for next update
                time.sleep(SYSTEM_CONFIG.update_interval_seconds)
                
        except KeyboardInterrupt:
            self.logger.info("Live trading stopped by user")
        except Exception as e:
            self.logger.error(f"Error in live trading: {e}")
        finally:
            self.is_running = False
    
    def process_symbol_live(self, symbol: str):
        """Process single symbol in live trading"""
        try:
            # Get latest market data
            quote = self.market_data_manager.get_live_quote(symbol)
            if not quote:
                return
            
            # Create market data object (simplified for live quote)
            market_data = MarketData(
                timestamp=quote["timestamp"],
                open=quote["price"],
                high=quote["price"],
                low=quote["price"],
                close=quote["price"],
                volume=0  # Not available in quote
            )
            
            # Process the data
            signal = self.process_market_data(symbol, market_data)
            if signal:
                self.trading_signals.append(signal)
                
                # Log significant events
                if signal.pattern.entry_signal:
                    self.logger.info(f"ENTRY SIGNAL: {symbol} at ${signal.market_data.close:.2f}")
                elif signal.pattern.exit_signal:
                    self.logger.info(f"EXIT SIGNAL: {symbol} at ${signal.market_data.close:.2f}")
                
        except Exception as e:
            self.logger.error(f"Error processing {symbol} live: {e}")
    
    def stop_trading(self):
        """Stop the trading system"""
        self.is_running = False
        self.logger.info("Trading system stopped")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status"""
        return {
            "is_running": self.is_running,
            "current_symbol": self.current_symbol,
            "total_signals": len(self.trading_signals),
            "trading_status": self.trading_engine.get_current_status(),
            "last_update": datetime.now().isoformat()
        }
    
    def export_results(self, filename: str = None) -> str:
        """Export trading results to file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{SYSTEM_CONFIG.results_directory}/trading_results_{timestamp}.json"
        
        results = {
            "system_config": {
                "trading_config": TRADING_CONFIG.__dict__,
                "system_config": SYSTEM_CONFIG.__dict__
            },
            "trading_performance": self.trading_engine.get_performance_summary(),
            "signals_summary": {
                "total_signals": len(self.trading_signals),
                "entry_signals": sum(1 for s in self.trading_signals if s.pattern.entry_signal),
                "exit_signals": sum(1 for s in self.trading_signals if s.pattern.exit_signal)
            },
            "trades": [
                {
                    "entry_time": trade.entry_timestamp.isoformat(),
                    "exit_time": trade.exit_timestamp.isoformat(),
                    "entry_price": trade.entry_price,
                    "exit_price": trade.exit_price,
                    "quantity": trade.quantity,
                    "pnl": trade.pnl,
                    "duration_bars": trade.duration_bars
                }
                for trade in self.trading_engine.trades
            ]
        }
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        self.logger.info(f"Results exported to {filename}")
        return filename
