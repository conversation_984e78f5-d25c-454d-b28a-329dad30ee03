"""
Data models for the automated trading system.
"""
from dataclasses import dataclass
from typing import List, Optional
from datetime import datetime
from enum import Enum


class MomentumColor(Enum):
    RED = "red"
    YELLOW = "yellow"
    GREEN = "green"


class ATRSignal(Enum):
    BLUE = "blue"
    RED = "red"
    NEUTRAL = "neutral"


class PositionStatus(Enum):
    OUT = 0
    IN = 1


@dataclass
class MarketData:
    """Single bar of market data"""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int


@dataclass
class TechnicalIndicators:
    """Technical indicators for a single bar"""
    momentum: float
    momentum_color: MomentumColor
    atr_value: float
    atr_signal: ATRSignal
    atr_confirmed: bool


@dataclass
class PatternSignal:
    """Pattern detection results"""
    declining_red_momentum: bool
    momentum_reversal: bool
    atr_confirmation: bool
    bar_pattern_detected: bool
    entry_signal: bool
    exit_signal: bool


@dataclass
class TradingSignal:
    """Complete trading signal with all components"""
    timestamp: datetime
    market_data: MarketData
    indicators: TechnicalIndicators
    pattern: PatternSignal
    position_status: PositionStatus
    entry_price: Optional[float] = None
    exit_price: Optional[float] = None


@dataclass
class Position:
    """Active trading position"""
    entry_timestamp: datetime
    entry_price: float
    quantity: int
    current_price: float
    unrealized_pnl: float
    
    def update_current_price(self, price: float):
        """Update current price and calculate unrealized P&L"""
        self.current_price = price
        self.unrealized_pnl = (price - self.entry_price) * self.quantity


@dataclass
class Trade:
    """Completed trade record"""
    entry_timestamp: datetime
    exit_timestamp: datetime
    entry_price: float
    exit_price: float
    quantity: int
    pnl: float
    duration_bars: int
    pattern_type: str


@dataclass
class PerformanceMetrics:
    """Trading system performance metrics"""
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    total_pnl: float = 0.0
    win_rate: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    
    def update_metrics(self, trades: List[Trade]):
        """Update performance metrics from trade list"""
        if not trades:
            return
            
        self.total_trades = len(trades)
        winning_trades = [t for t in trades if t.pnl > 0]
        losing_trades = [t for t in trades if t.pnl < 0]
        
        self.winning_trades = len(winning_trades)
        self.losing_trades = len(losing_trades)
        self.total_pnl = sum(t.pnl for t in trades)
        
        if self.total_trades > 0:
            self.win_rate = self.winning_trades / self.total_trades
            
        if winning_trades:
            self.avg_win = sum(t.pnl for t in winning_trades) / len(winning_trades)
            
        if losing_trades:
            self.avg_loss = sum(t.pnl for t in losing_trades) / len(losing_trades)
