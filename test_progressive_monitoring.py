"""
Test script for Progressive Pattern Monitoring System
"""
import time
from datetime import datetime, timedelta
import numpy as np

from progressive_pattern_monitor import ProgressivePatternMonitor, PatternStage
from data_models import MarketData, MomentumColor


def create_test_market_data(symbol: str, scenario: str) -> list:
    """Create test market data for different scenarios"""
    base_time = datetime.now() - timedelta(hours=1)
    market_data = []
    base_price = 100.0
    
    if scenario == "complete_pattern":
        # Create a complete momentum reversal pattern
        print(f"  Creating complete pattern for {symbol}")
        
        # Phase 1: 5 declining red momentum bars
        for i in range(5):
            decline = 0.5 + (i * 0.3)  # Increasing decline
            price = base_price - decline
            
            data = MarketData(
                timestamp=base_time + timedelta(minutes=i),
                open=base_price,
                high=base_price + 0.1,
                low=price - 0.1,
                close=price,
                volume=1000 + i * 100
            )
            market_data.append(data)
            base_price = price
        
        # Phase 2: Momentum reversal (yellow bar)
        recovery_price = base_price + 0.8
        data = MarketData(
            timestamp=base_time + timedelta(minutes=5),
            open=base_price,
            high=recovery_price + 0.2,
            low=base_price - 0.1,
            close=recovery_price,
            volume=1500
        )
        market_data.append(data)
    
    elif scenario == "building_pattern":
        # Create a pattern building toward completion
        print(f"  Creating building pattern for {symbol}")
        
        # 3 declining red momentum bars (not yet complete)
        for i in range(3):
            decline = 0.4 + (i * 0.2)
            price = base_price - decline
            
            data = MarketData(
                timestamp=base_time + timedelta(minutes=i),
                open=base_price,
                high=base_price + 0.1,
                low=price - 0.1,
                close=price,
                volume=1000 + i * 100
            )
            market_data.append(data)
            base_price = price
    
    elif scenario == "failed_pattern":
        # Create a pattern that fails
        print(f"  Creating failed pattern for {symbol}")
        
        # 2 declining bars, then recovery (breaks pattern)
        for i in range(2):
            decline = 0.3 + (i * 0.2)
            price = base_price - decline
            
            data = MarketData(
                timestamp=base_time + timedelta(minutes=i),
                open=base_price,
                high=base_price + 0.1,
                low=price - 0.1,
                close=price,
                volume=1000
            )
            market_data.append(data)
            base_price = price
        
        # Recovery that breaks the pattern
        recovery_price = base_price + 1.5
        data = MarketData(
            timestamp=base_time + timedelta(minutes=2),
            open=base_price,
            high=recovery_price + 0.3,
            low=base_price,
            close=recovery_price,
            volume=1200
        )
        market_data.append(data)
    
    return market_data


def test_progressive_monitoring():
    """Test the progressive pattern monitoring system"""
    print("=" * 80)
    print("TESTING PROGRESSIVE PATTERN MONITORING SYSTEM")
    print("=" * 80)
    
    # Initialize monitor
    monitor = ProgressivePatternMonitor(min_declining_bars=4, max_watch_age_minutes=30)
    
    # Test scenarios
    test_cases = [
        ("AAPL", "complete_pattern"),
        ("MSFT", "building_pattern"),
        ("GOOGL", "failed_pattern"),
        ("TSLA", "building_pattern"),
        ("NVDA", "complete_pattern")
    ]
    
    print(f"\nTesting {len(test_cases)} scenarios...")
    print("-" * 60)
    
    # Process each test case
    for symbol, scenario in test_cases:
        print(f"\n📊 Testing {symbol} - {scenario}")
        
        # Create test data
        market_data_list = create_test_market_data(symbol, scenario)
        
        # Process each data point
        for data_point in market_data_list:
            progress = monitor.analyze_stock_progression(symbol, data_point)
            if progress:
                monitor.add_to_watch_list(symbol, progress)
                
                print(f"    Bar: Price=${data_point.close:.2f}, "
                      f"Stage={progress.stage.value}, "
                      f"Declining={progress.consecutive_declining_bars}, "
                      f"Strength={progress.pattern_strength:.2f}")
    
    # Display results
    print(f"\n" + "=" * 80)
    print("MONITORING RESULTS")
    print("=" * 80)
    
    # Get statistics
    stats = monitor.get_statistics()
    print(f"Total stocks monitored: {stats['total_monitored']}")
    print(f"Current watch list size: {stats['current_watch_list']}")
    print(f"Patterns completed: {stats['patterns_completed_today']}")
    print(f"Patterns failed: {stats['patterns_failed_today']}")
    
    # Show watch list by stage
    by_stage = monitor.get_watch_list_by_stage()
    print(f"\nWatch List by Stage:")
    print("-" * 40)
    
    for stage, patterns in by_stage.items():
        if patterns:
            print(f"{stage.value}: {len(patterns)} stocks")
            for pattern in patterns:
                print(f"  {pattern.symbol}: {pattern.consecutive_declining_bars} declining bars, "
                      f"strength {pattern.pattern_strength:.2f}")
    
    # Show completed patterns
    completed = monitor.get_completed_patterns()
    if completed:
        print(f"\n🎯 COMPLETED PATTERNS (Ready to Trade):")
        print("-" * 50)
        for pattern in completed:
            print(f"  {pattern.symbol}: All criteria met!")
            print(f"    Declining bars: {pattern.consecutive_declining_bars}")
            print(f"    Momentum reversal: {pattern.momentum_reversal_detected}")
            print(f"    ATR confirmed: {pattern.atr_confirmed}")
            print(f"    Pattern strength: {pattern.pattern_strength:.2f}")
    else:
        print(f"\n📊 No completed patterns yet - system is building watch list")
    
    # Show priority list
    priority_list = monitor.get_priority_watch_list(10)
    if priority_list:
        print(f"\n📈 PRIORITY WATCH LIST (Top Opportunities):")
        print("-" * 60)
        print(f"{'Symbol':<8} {'Stage':<15} {'Declining':<10} {'Strength':<10} {'Price':<10}")
        print("-" * 60)
        
        for pattern in priority_list:
            stage_name = pattern.stage.value.replace('_', ' ').title()
            print(f"{pattern.symbol:<8} {stage_name:<15} {pattern.consecutive_declining_bars:<10} "
                  f"{pattern.pattern_strength:<10.2f} ${pattern.current_price:<9.2f}")
    
    return monitor


def test_pattern_progression():
    """Test pattern progression through stages"""
    print(f"\n" + "=" * 80)
    print("TESTING PATTERN PROGRESSION THROUGH STAGES")
    print("=" * 80)
    
    monitor = ProgressivePatternMonitor()
    symbol = "TEST"
    
    print(f"Simulating {symbol} progressing through pattern stages...")
    
    base_time = datetime.now()
    base_price = 150.0
    
    # Stage 1: Start with 1 declining bar
    print(f"\n📉 Stage 1: Early decline")
    data = MarketData(
        timestamp=base_time,
        open=base_price,
        high=base_price,
        low=base_price - 1.0,
        close=base_price - 0.8,
        volume=1000
    )
    
    progress = monitor.analyze_stock_progression(symbol, data)
    monitor.add_to_watch_list(symbol, progress)
    print(f"  {symbol}: Stage={progress.stage.value}, Declining={progress.consecutive_declining_bars}")
    
    # Stage 2: Add more declining bars
    print(f"\n📉 Stage 2: Building pattern")
    for i in range(2, 5):  # Add bars 2, 3, 4
        base_price -= 0.6
        data = MarketData(
            timestamp=base_time + timedelta(minutes=i),
            open=base_price + 0.6,
            high=base_price + 0.6,
            low=base_price - 0.2,
            close=base_price,
            volume=1000 + i * 100
        )
        
        progress = monitor.analyze_stock_progression(symbol, data)
        print(f"  Bar {i}: Stage={progress.stage.value}, Declining={progress.consecutive_declining_bars}")
    
    # Stage 3: Momentum reversal
    print(f"\n🔄 Stage 3: Momentum reversal")
    base_price += 1.2  # Recovery
    data = MarketData(
        timestamp=base_time + timedelta(minutes=5),
        open=base_price - 1.2,
        high=base_price + 0.3,
        low=base_price - 1.2,
        close=base_price,
        volume=1500
    )
    
    progress = monitor.analyze_stock_progression(symbol, data)
    print(f"  Reversal bar: Stage={progress.stage.value}, Reversal={progress.momentum_reversal_detected}")
    
    # Check for completion
    completed = monitor.get_completed_patterns()
    if completed:
        print(f"\n🎯 PATTERN COMPLETED! {symbol} is ready to trade")
    else:
        print(f"\n⏳ Pattern not yet complete - waiting for ATR confirmation")
    
    return monitor


def main():
    """Main test function"""
    print("🧪 PROGRESSIVE PATTERN MONITORING SYSTEM TESTS")
    
    # Test 1: Basic progressive monitoring
    monitor1 = test_progressive_monitoring()
    
    # Test 2: Pattern progression through stages
    monitor2 = test_pattern_progression()
    
    print(f"\n" + "=" * 80)
    print("✅ ALL TESTS COMPLETED")
    print("=" * 80)
    
    print(f"\n🎯 Key Features Demonstrated:")
    print(f"✅ Progressive pattern stage tracking")
    print(f"✅ Intelligent watch list management")
    print(f"✅ Pattern completion detection")
    print(f"✅ Pattern failure handling")
    print(f"✅ Priority-based monitoring")
    print(f"✅ Real-time pattern strength calculation")
    
    print(f"\n📊 This system enables:")
    print(f"• Efficient resource usage (focus on promising candidates)")
    print(f"• Early pattern detection (catch setups as they develop)")
    print(f"• Automatic pattern validation (ensure all criteria met)")
    print(f"• Smart failure handling (remove broken patterns)")
    print(f"• Prioritized monitoring (focus on closest to completion)")


if __name__ == "__main__":
    main()
