"""
Realistic House Money Strategy Demo with proper position sizing.
"""
from datetime import datetime, timedelta
from data_models import MarketData, TechnicalIndicators, TradingSignal, PatternSignal, PositionStatus, MomentumColor, ATRSignal
from trading_engine import TradingEngine
from config import TRADING_CONFIG


def create_trading_signal(timestamp: datetime, price: float, is_entry: bool = True) -> TradingSignal:
    """Create a trading signal for the demo"""
    market_data = MarketData(
        timestamp=timestamp,
        open=price,
        high=price + 0.5,
        low=price - 0.5,
        close=price,
        volume=1000
    )
    
    indicators = TechnicalIndicators(
        momentum=2.0 if is_entry else -1.0,
        momentum_color=MomentumColor.YELLOW if is_entry else MomentumColor.RED,
        atr_value=1.5,
        atr_signal=ATRSignal.BLUE if is_entry else ATRSignal.RED,
        atr_confirmed=True
    )
    
    pattern = PatternSignal(
        declining_red_momentum=True,
        momentum_reversal=is_entry,
        atr_confirmation=is_entry,
        bar_pattern_detected=True,
        entry_signal=is_entry,
        exit_signal=not is_entry
    )
    
    return TradingSignal(
        timestamp=timestamp,
        market_data=market_data,
        indicators=indicators,
        pattern=pattern,
        position_status=PositionStatus.OUT
    )


def simulate_realistic_trading_day():
    """Simulate a realistic trading day with house money strategy"""
    print("=" * 80)
    print("REALISTIC HOUSE MONEY STRATEGY SIMULATION")
    print("=" * 80)
    print(f"Daily Target: ${TRADING_CONFIG.daily_profit_target:.2f}")
    print(f"Initial Profit Lock: ${TRADING_CONFIG.initial_profit_lock:.2f}")
    print(f"House Money Multiplier: {TRADING_CONFIG.house_money_multiplier:.1f}x")
    print("=" * 80)
    
    # Initialize with smaller capital for more realistic demo
    engine = TradingEngine(initial_capital=5000, position_size=0.10)  # 10% position size for demo
    
    base_time = datetime.now() - timedelta(hours=2)
    
    print(f"\nStarting Day with ${engine.current_capital:,.2f}")
    print(f"Position Size: {engine.position_size:.0%} of capital")
    
    # Trade scenarios with realistic price movements
    trades = [
        {"entry_price": 150.00, "exit_price": 152.50, "description": "Small winner - building toward profit lock"},
        {"entry_price": 153.00, "exit_price": 156.00, "description": "Good winner - should trigger profit lock"},
        {"entry_price": 157.00, "exit_price": 160.50, "description": "House money trade - larger position"},
        {"entry_price": 161.00, "exit_price": 164.00, "description": "Another house money trade"},
        {"entry_price": 165.00, "exit_price": 167.50, "description": "Final push to daily target"},
    ]
    
    for i, trade_info in enumerate(trades, 1):
        print(f"\n" + "─" * 60)
        print(f"TRADE {i}: {trade_info['description']}")
        print("─" * 60)
        
        # Get pre-trade status
        status = engine.get_current_status()
        print(f"Before Trade {i}:")
        print(f"  Daily P&L: ${status['daily_pnl']:.2f} / ${status['daily_target']:.2f}")
        print(f"  Progress: {status['progress_percent']:.1f}%")
        print(f"  Trading Phase: {status['trading_phase']}")
        print(f"  Position Multiplier: {status['position_multiplier']:.1f}x")
        
        if status['locked_profit'] > 0:
            print(f"  💰 Locked Profit: ${status['locked_profit']:.2f}")
            print(f"  🏠 House Money Available: ${status['house_money_available']:.2f}")
        
        if status['target_reached']:
            print(f"  🎉 Daily target already reached - stopping trading")
            break
        
        # Entry
        entry_time = base_time + timedelta(minutes=i*30)
        entry_signal = create_trading_signal(entry_time, trade_info['entry_price'], is_entry=True)
        
        if engine.enter_position(entry_signal):
            print(f"  ✅ Entered: {engine.current_position.quantity} shares at ${trade_info['entry_price']:.2f}")
            
            # Calculate expected P&L
            expected_pnl = (trade_info['exit_price'] - trade_info['entry_price']) * engine.current_position.quantity
            print(f"  📈 Expected P&L: ${expected_pnl:.2f}")
            
            # Update position price
            engine.update_position(trade_info['exit_price'])
            
            # Exit
            exit_time = entry_time + timedelta(minutes=15)
            exit_signal = create_trading_signal(exit_time, trade_info['exit_price'], is_entry=False)
            
            if engine.exit_position(exit_signal):
                print(f"  ✅ Exited: at ${trade_info['exit_price']:.2f}")
                
                # Show post-trade status
                new_status = engine.get_current_status()
                print(f"\n  After Trade {i}:")
                print(f"    Daily P&L: ${new_status['daily_pnl']:.2f}")
                print(f"    Progress: {new_status['progress_percent']:.1f}%")
                print(f"    Phase: {new_status['trading_phase']}")
                
                if new_status['target_reached']:
                    print(f"    🎉 DAILY TARGET REACHED!")
                    break
        else:
            print(f"  ❌ Entry rejected")
    
    # Final summary
    print(f"\n" + "=" * 80)
    print("END OF DAY SUMMARY")
    print("=" * 80)
    
    final_status = engine.get_current_status()
    
    print(f"Final Daily P&L: ${final_status['daily_pnl']:.2f}")
    print(f"Daily Target: ${final_status['daily_target']:.2f}")
    print(f"Target Achievement: {final_status['progress_percent']:.1f}%")
    print(f"Target Reached: {'✅ YES' if final_status['target_reached'] else '❌ NO'}")
    
    if final_status['locked_profit'] > 0:
        print(f"Profit Locked: ${final_status['locked_profit']:.2f}")
        print(f"House Money Trades: {final_status.get('house_money_trades', 0)}")
    
    # Show all trades
    print(f"\nTrade Details:")
    for i, trade in enumerate(engine.trades, 1):
        print(f"  Trade {i}: ${trade.entry_price:.2f} → ${trade.exit_price:.2f} = ${trade.pnl:.2f}")
    
    total_pnl = sum(trade.pnl for trade in engine.trades)
    print(f"\nTotal P&L: ${total_pnl:.2f}")
    print(f"Win Rate: {len([t for t in engine.trades if t.pnl > 0]) / len(engine.trades) * 100:.0f}%")
    
    # Show house money strategy effectiveness
    print(f"\n" + "─" * 60)
    print("HOUSE MONEY STRATEGY ANALYSIS")
    print("─" * 60)
    
    if final_status['locked_profit'] > 0:
        print(f"✅ Successfully locked initial profit: ${final_status['locked_profit']:.2f}")
        print(f"✅ Used house money for larger positions")
        print(f"✅ Protected capital while pursuing daily target")
    else:
        print(f"📈 Building toward profit lock threshold of ${TRADING_CONFIG.initial_profit_lock:.2f}")
    
    if final_status['target_reached']:
        print(f"🎉 Daily target of ${TRADING_CONFIG.daily_profit_target:.2f} achieved!")
        print(f"💰 Total profit: ${final_status['daily_pnl']:.2f}")
    else:
        remaining = TRADING_CONFIG.daily_profit_target - final_status['daily_pnl']
        print(f"📊 ${remaining:.2f} remaining to reach daily target")


def explain_house_money_phases():
    """Explain the different phases of house money strategy"""
    print(f"\n" + "=" * 80)
    print("HOUSE MONEY STRATEGY PHASES EXPLAINED")
    print("=" * 80)
    
    print(f"🔵 PHASE 1: INITIAL CAPITAL")
    print(f"   • Trade with normal position size")
    print(f"   • Focus on building initial profit")
    print(f"   • Target: Reach ${TRADING_CONFIG.initial_profit_lock:.2f} profit")
    
    print(f"\n💰 PHASE 2: PROFIT LOCK")
    print(f"   • Lock in ${TRADING_CONFIG.initial_profit_lock:.2f} profit")
    print(f"   • Protect {TRADING_CONFIG.profit_protection_percent:.0%} of additional profits")
    print(f"   • Transition to house money trading")
    
    print(f"\n🏠 PHASE 3: HOUSE MONEY")
    print(f"   • Increase position size by {TRADING_CONFIG.house_money_multiplier:.1f}x")
    print(f"   • Trade with profits, not original capital")
    print(f"   • Target: Reach ${TRADING_CONFIG.daily_profit_target:.2f} daily goal")
    
    print(f"\n🎯 PHASE 4: TARGET REACHED")
    print(f"   • Stop trading for the day")
    print(f"   • Preserve profits")
    print(f"   • Reset for next trading day")
    
    print(f"\n" + "─" * 60)
    print("KEY BENEFITS:")
    print("─" * 60)
    print("✅ Take profit first - secure initial gains")
    print("✅ Play with house money - use profits for bigger bets")
    print("✅ Protect the base - never risk original capital")
    print("✅ Systematic approach - consistent daily targets")
    print("✅ Risk management - built-in profit protection")


def main():
    """Main demonstration"""
    print("HOUSE MONEY STRATEGY")
    print("Take Profit First, Then Play with House Money to Reach $100/Day")
    
    try:
        # Explain the strategy
        explain_house_money_phases()
        
        # Run realistic simulation
        simulate_realistic_trading_day()
        
        print(f"\n" + "=" * 80)
        print("HOUSE MONEY STRATEGY DEMO COMPLETE!")
        print("=" * 80)
        
        print(f"\n🎯 This strategy implements your exact request:")
        print(f"✅ Take initial profit first (${TRADING_CONFIG.initial_profit_lock:.2f})")
        print(f"✅ Play with house money (profits) for larger positions")
        print(f"✅ Target ${TRADING_CONFIG.daily_profit_target:.2f} daily profit")
        print(f"✅ Protect capital while being aggressive with gains")
        
        print(f"\nTo use with real trading:")
        print(f"  python main.py --mode backtest --symbol SPY")
        print(f"  python main.py --mode live --symbols SPY QQQ")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
