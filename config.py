"""
Configuration settings for the automated trading system.
"""
import os
from dataclasses import dataclass
from typing import List


@dataclass
class APIConfig:
    """API configuration settings"""
    # Alpaca API credentials
    alpaca_api_key: str = "PKUQBMURWJZ7IW64SE8A"
    alpaca_secret_key: str = "dx331VMHCNncAf8NoiwoMhNjucFizl55xfP9YqOJ"
    alpaca_paper_trading: bool = True
    
    # Financial Modeling Prep API
    fmp_api_key: str = "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7"


@dataclass
class TradingConfig:
    """Trading system configuration"""
    # Position sizing
    position_size_percent: float = 0.02  # 2% of capital per trade
    initial_capital: float = 100000.0    # Starting capital
    
    # Risk management
    max_positions: int = 1               # Maximum concurrent positions
    stop_loss_percent: float = 0.05      # 5% stop loss
    take_profit_percent: float = 0.10    # 10% take profit
    
    # Pattern detection parameters
    min_declining_bars: int = 4          # Minimum declining red momentum bars
    momentum_period: int = 14            # Momentum calculation period
    atr_period: int = 14                 # ATR calculation period
    atr_multiplier: float = 2.0          # ATR trailing stop multiplier
    
    # Trading hours (24-hour format)
    trading_start_hour: int = 9          # 9:30 AM market open
    trading_start_minute: int = 30
    trading_end_hour: int = 16           # 4:00 PM market close
    trading_end_minute: int = 0
    
    # Data settings
    data_timeframe: str = "1min"         # Data timeframe
    lookback_days: int = 5               # Days of historical data
    
    # Symbols to trade
    symbols: List[str] = None
    
    def __post_init__(self):
        if self.symbols is None:
            self.symbols = [
                "SPY",   # S&P 500 ETF
                "QQQ",   # NASDAQ ETF
                "IWM",   # Russell 2000 ETF
                "AAPL",  # Apple
                "MSFT",  # Microsoft
                "GOOGL", # Google
                "AMZN",  # Amazon
                "TSLA",  # Tesla
                "NVDA",  # NVIDIA
                "META"   # Meta
            ]


@dataclass
class SystemConfig:
    """System configuration"""
    # Logging
    log_level: str = "INFO"
    log_file: str = "trading_system.log"
    
    # Data storage
    data_directory: str = "data"
    results_directory: str = "results"
    
    # System settings
    update_interval_seconds: int = 60    # Update frequency
    max_retries: int = 3                 # API retry attempts
    retry_delay_seconds: int = 5         # Delay between retries
    
    # Backtesting
    backtest_start_date: str = "2024-01-01"
    backtest_end_date: str = "2024-12-31"
    
    # Notifications (placeholder for future implementation)
    enable_email_notifications: bool = False
    enable_slack_notifications: bool = False
    
    def __post_init__(self):
        # Create directories if they don't exist
        os.makedirs(self.data_directory, exist_ok=True)
        os.makedirs(self.results_directory, exist_ok=True)


# Global configuration instances
API_CONFIG = APIConfig()
TRADING_CONFIG = TradingConfig()
SYSTEM_CONFIG = SystemConfig()


def get_config() -> dict:
    """Get all configuration as dictionary"""
    return {
        "api": API_CONFIG,
        "trading": TRADING_CONFIG,
        "system": SYSTEM_CONFIG
    }


def update_config(**kwargs):
    """Update configuration values"""
    for key, value in kwargs.items():
        if hasattr(API_CONFIG, key):
            setattr(API_CONFIG, key, value)
        elif hasattr(TRADING_CONFIG, key):
            setattr(TRADING_CONFIG, key, value)
        elif hasattr(SYSTEM_CONFIG, key):
            setattr(SYSTEM_CONFIG, key, value)


def validate_config() -> bool:
    """Validate configuration settings"""
    errors = []
    
    # Validate API keys
    if not API_CONFIG.alpaca_api_key:
        errors.append("Alpaca API key is required")
    
    if not API_CONFIG.alpaca_secret_key:
        errors.append("Alpaca secret key is required")
    
    if not API_CONFIG.fmp_api_key:
        errors.append("FMP API key is required")
    
    # Validate trading parameters
    if TRADING_CONFIG.position_size_percent <= 0 or TRADING_CONFIG.position_size_percent > 1:
        errors.append("Position size percent must be between 0 and 1")
    
    if TRADING_CONFIG.initial_capital <= 0:
        errors.append("Initial capital must be positive")
    
    if TRADING_CONFIG.min_declining_bars < 2:
        errors.append("Minimum declining bars must be at least 2")
    
    # Validate symbols
    if not TRADING_CONFIG.symbols:
        errors.append("At least one trading symbol is required")
    
    if errors:
        print("Configuration validation errors:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    return True


# Environment variable overrides
def load_from_environment():
    """Load configuration from environment variables"""
    # API keys
    if os.getenv("ALPACA_API_KEY"):
        API_CONFIG.alpaca_api_key = os.getenv("ALPACA_API_KEY")
    
    if os.getenv("ALPACA_SECRET_KEY"):
        API_CONFIG.alpaca_secret_key = os.getenv("ALPACA_SECRET_KEY")
    
    if os.getenv("FMP_API_KEY"):
        API_CONFIG.fmp_api_key = os.getenv("FMP_API_KEY")
    
    # Trading settings
    if os.getenv("INITIAL_CAPITAL"):
        TRADING_CONFIG.initial_capital = float(os.getenv("INITIAL_CAPITAL"))
    
    if os.getenv("POSITION_SIZE_PERCENT"):
        TRADING_CONFIG.position_size_percent = float(os.getenv("POSITION_SIZE_PERCENT"))
    
    # Paper trading flag
    if os.getenv("PAPER_TRADING"):
        API_CONFIG.alpaca_paper_trading = os.getenv("PAPER_TRADING").lower() == "true"


# Load environment variables on import
load_from_environment()
