"""
Launcher for the Automated Momentum Reversal Trading System
"""
import os
import sys
import subprocess
from config import validate_config, API_CONFIG, TRADING_CONFIG


def print_banner():
    """Print system banner"""
    print("=" * 80)
    print("🎯 AUTOMATED MOMENTUM REVERSAL TRADING SYSTEM")
    print("S&P 500 + $100B Market Cap Scanner with House Money Strategy")
    print("=" * 80)
    print(f"API Status: Alpaca ✅ | FMP ✅ | Paper Trading: {API_CONFIG.alpaca_paper_trading}")
    print(f"Daily Target: ${TRADING_CONFIG.daily_profit_target} | Profit Lock: ${TRADING_CONFIG.initial_profit_lock}")
    print("=" * 80)


def check_system():
    """Check system requirements and configuration"""
    print("🔍 Checking system configuration...")
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed!")
        return False
    
    print("✅ Configuration validated")
    print(f"✅ Alpaca API: {API_CONFIG.alpaca_api_key[:10]}...")
    print(f"✅ FMP API: {API_CONFIG.fmp_api_key[:10]}...")
    print(f"✅ Paper Trading: {API_CONFIG.alpaca_paper_trading}")
    
    return True


def launch_gui():
    """Launch the desktop GUI"""
    print("🖥️  Launching Desktop GUI...")
    print("The GUI will open in a new window with:")
    print("  • Real-time S&P 500 + $100B market cap scanning")
    print("  • House money strategy monitoring")
    print("  • Live trading controls")
    print("  • Pattern detection results")

    try:
        subprocess.run([sys.executable, "trading_gui.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error launching GUI: {e}")
    except FileNotFoundError:
        print("❌ GUI file not found. Please ensure trading_gui.py exists.")


def launch_simple_gui():
    """Launch the simple GUI"""
    print("📱 Launching Simple GUI...")
    print("Lightweight interface with:")
    print("  • S&P 500 + $100B market cap scanning")
    print("  • Real-time results display")
    print("  • System logging")
    print("  • Easy-to-use controls")

    try:
        subprocess.run([sys.executable, "simple_gui.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error launching Simple GUI: {e}")
    except FileNotFoundError:
        print("❌ Simple GUI file not found. Please ensure simple_gui.py exists.")


def launch_cli():
    """Launch the command-line interface"""
    print("💻 Launching Command-Line Interface...")
    print("Interactive menu with scanning and analysis options")
    
    try:
        subprocess.run([sys.executable, "scanner_cli.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error launching CLI: {e}")
    except FileNotFoundError:
        print("❌ CLI file not found. Please ensure scanner_cli.py exists.")


def launch_continuous_scanner_gui():
    """Launch continuous scanner GUI"""
    print("🔄 Launching Continuous Scanner GUI...")
    print("Real-time progressive pattern monitoring with:")
    print("  • Continuous S&P 500 + $100B market cap scanning")
    print("  • Progressive pattern stage tracking")
    print("  • Intelligent watch list management")
    print("  • Real-time entry signal generation")

    try:
        subprocess.run([sys.executable, "continuous_scanner_gui.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error launching Continuous Scanner GUI: {e}")
    except FileNotFoundError:
        print("❌ Continuous Scanner GUI file not found.")


def launch_continuous_scan():
    """Launch continuous scanning mode"""
    print("🔄 Launching Continuous Scanning Mode...")
    print("This will scan the entire universe every 2 minutes during market hours")
    print("Press Ctrl+C to stop")

    try:
        subprocess.run([sys.executable, "continuous_scanner.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error launching continuous scan: {e}")
    except KeyboardInterrupt:
        print("\n🛑 Continuous scanning stopped")


def run_quick_scan():
    """Run a quick focused scan"""
    print("⚡ Running Quick Scan...")
    print("Scanning high-volume stocks for immediate opportunities")
    
    try:
        subprocess.run([sys.executable, "focused_scanner.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running quick scan: {e}")


def run_backtest():
    """Run a backtest"""
    print("📊 Running Backtest...")
    symbol = input("Enter symbol to backtest (default: SPY): ").strip().upper()
    if not symbol:
        symbol = "SPY"

    try:
        subprocess.run([sys.executable, "main.py", "--mode", "backtest", "--symbol", symbol], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running backtest: {e}")


def test_progressive_monitoring():
    """Test progressive monitoring system"""
    print("🧪 Testing Progressive Monitoring System...")
    print("This will demonstrate pattern stage tracking and watch list management")

    try:
        subprocess.run([sys.executable, "test_progressive_monitoring.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running test: {e}")
    except FileNotFoundError:
        print("❌ Test file not found.")


def show_system_status():
    """Show current system status"""
    print("\n📊 SYSTEM STATUS")
    print("-" * 50)
    print(f"Configuration: {'✅ Valid' if validate_config() else '❌ Invalid'}")
    print(f"Alpaca API: ✅ Connected")
    print(f"FMP API: ✅ Connected")
    print(f"Paper Trading: {'✅ Enabled' if API_CONFIG.alpaca_paper_trading else '⚠️ LIVE TRADING'}")
    
    print(f"\n💰 HOUSE MONEY STRATEGY:")
    print(f"  Daily Target: ${TRADING_CONFIG.daily_profit_target}")
    print(f"  Initial Profit Lock: ${TRADING_CONFIG.initial_profit_lock}")
    print(f"  House Money Multiplier: {TRADING_CONFIG.house_money_multiplier}x")
    print(f"  Profit Protection: {TRADING_CONFIG.profit_protection_percent:.0%}")
    
    print(f"\n🎯 PATTERN DETECTION:")
    print(f"  Momentum Period: {TRADING_CONFIG.momentum_period}")
    print(f"  ATR Period: {TRADING_CONFIG.atr_period}")
    print(f"  Min Declining Bars: {TRADING_CONFIG.min_declining_bars}")
    
    print(f"\n📈 POSITION SIZING:")
    print(f"  Initial Capital: ${TRADING_CONFIG.initial_capital:,.2f}")
    print(f"  Position Size: {TRADING_CONFIG.position_size_percent:.1%}")


def main_menu():
    """Main menu system"""
    while True:
        print_banner()
        
        print("\n🚀 LAUNCH OPTIONS:")
        print("1. 🖥️  Desktop GUI (Full Featured)")
        print("2. 📱 Simple GUI (Lightweight)")
        print("3. 🔄 Continuous Scanner GUI (Real-Time)")
        print("4. 💻 Command-Line Interface")
        print("5. 🔄 Continuous Scanning Mode (CLI)")
        print("6. ⚡ Quick Scan (High-Volume Stocks)")
        print("7. 📊 Run Backtest")
        print("8. 📋 Show System Status")
        print("9. 🔧 Test API Connections")
        print("10. 📖 View Documentation")
        print("11. 🧪 Test Progressive Monitoring")
        print("12. 🚪 Exit")
        
        choice = input("\nSelect option (1-12): ").strip()
        
        if choice == '1':
            if check_system():
                launch_gui()
            input("\nPress Enter to continue...")
        
        elif choice == '2':
            if check_system():
                launch_simple_gui()
            input("\nPress Enter to continue...")

        elif choice == '3':
            if check_system():
                launch_continuous_scanner_gui()
            input("\nPress Enter to continue...")

        elif choice == '4':
            if check_system():
                launch_cli()
            input("\nPress Enter to continue...")

        elif choice == '5':
            if check_system():
                launch_continuous_scan()
            input("\nPress Enter to continue...")

        elif choice == '6':
            if check_system():
                run_quick_scan()
            input("\nPress Enter to continue...")

        elif choice == '7':
            if check_system():
                run_backtest()
            input("\nPress Enter to continue...")

        elif choice == '8':
            show_system_status()
            input("\nPress Enter to continue...")

        elif choice == '9':
            print("🔧 Testing API connections...")
            try:
                subprocess.run([sys.executable, "test_api_connection.py"], check=True)
            except subprocess.CalledProcessError as e:
                print(f"❌ Error testing APIs: {e}")
            input("\nPress Enter to continue...")

        elif choice == '10':
            print("📖 Opening documentation...")
            print("\nAvailable documentation files:")
            print("  • README.md - Main documentation")
            print("  • HOUSE_MONEY_STRATEGY.md - House money strategy details")
            print("  • SYSTEM_OVERVIEW.md - Complete system overview")
            print("  • CONTINUOUS_SCANNING_SYSTEM.md - Continuous scanning details")

            doc_choice = input("\nWhich document? (1=README, 2=House Money, 3=System Overview, 4=Continuous Scanning): ").strip()

            if doc_choice == '1':
                os.system("type README.md" if os.name == 'nt' else "cat README.md")
            elif doc_choice == '2':
                os.system("type HOUSE_MONEY_STRATEGY.md" if os.name == 'nt' else "cat HOUSE_MONEY_STRATEGY.md")
            elif doc_choice == '3':
                os.system("type SYSTEM_OVERVIEW.md" if os.name == 'nt' else "cat SYSTEM_OVERVIEW.md")
            elif doc_choice == '4':
                os.system("type CONTINUOUS_SCANNING_SYSTEM.md" if os.name == 'nt' else "cat CONTINUOUS_SCANNING_SYSTEM.md")

            input("\nPress Enter to continue...")

        elif choice == '11':
            test_progressive_monitoring()
            input("\nPress Enter to continue...")

        elif choice == '12':
            print("👋 Thank you for using the Automated Momentum Reversal Trading System!")
            print("Remember: Take profit first, then play with house money to reach $100/day!")
            break
        
        else:
            print("❌ Invalid option. Please try again.")
            input("Press Enter to continue...")


def main():
    """Main entry point"""
    try:
        main_menu()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("Please check your configuration and try again.")


if __name__ == "__main__":
    main()
