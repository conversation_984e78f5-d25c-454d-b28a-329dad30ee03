"""
Market data provider using Financial Modeling Prep API and Alpaca.
"""
import requests
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
from data_models import MarketData


class FMPDataProvider:
    """Financial Modeling Prep data provider"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://financialmodelingprep.com/api/v3"
        self.logger = logging.getLogger(__name__)
    
    def get_historical_data(self, symbol: str, timeframe: str = "1min", 
                          days: int = 5) -> List[MarketData]:
        """Get historical market data"""
        try:
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # Format dates for API
            from_date = start_date.strftime("%Y-%m-%d")
            to_date = end_date.strftime("%Y-%m-%d")
            
            # Build URL based on timeframe
            if timeframe == "1min":
                url = f"{self.base_url}/historical-chart/1min/{symbol}"
                params = {
                    "from": from_date,
                    "to": to_date,
                    "apikey": self.api_key
                }
            else:
                url = f"{self.base_url}/historical-price-full/{symbol}"
                params = {
                    "from": from_date,
                    "to": to_date,
                    "apikey": self.api_key
                }
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            # Parse response based on timeframe
            if timeframe == "1min":
                bars = data if isinstance(data, list) else []
            else:
                bars = data.get("historical", [])
            
            market_data = []
            for bar in bars:
                try:
                    # Parse datetime
                    if "date" in bar:
                        timestamp = datetime.fromisoformat(bar["date"].replace("Z", "+00:00"))
                    else:
                        continue
                    
                    market_data.append(MarketData(
                        timestamp=timestamp,
                        open=float(bar.get("open", 0)),
                        high=float(bar.get("high", 0)),
                        low=float(bar.get("low", 0)),
                        close=float(bar.get("close", 0)),
                        volume=int(bar.get("volume", 0))
                    ))
                except (ValueError, KeyError) as e:
                    self.logger.warning(f"Error parsing bar data: {e}")
                    continue
            
            # Sort by timestamp
            market_data.sort(key=lambda x: x.timestamp)
            
            self.logger.info(f"Retrieved {len(market_data)} bars for {symbol}")
            return market_data
            
        except requests.RequestException as e:
            self.logger.error(f"Error fetching data from FMP: {e}")
            return []
        except Exception as e:
            self.logger.error(f"Unexpected error: {e}")
            return []
    
    def get_real_time_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get real-time quote"""
        try:
            url = f"{self.base_url}/quote/{symbol}"
            params = {"apikey": self.api_key}
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            if data and len(data) > 0:
                quote = data[0]
                return {
                    "symbol": quote.get("symbol"),
                    "price": float(quote.get("price", 0)),
                    "change": float(quote.get("change", 0)),
                    "changesPercentage": float(quote.get("changesPercentage", 0)),
                    "timestamp": datetime.now()
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error fetching real-time quote: {e}")
            return None


class AlpacaDataProvider:
    """Alpaca data provider for live trading"""
    
    def __init__(self, api_key: str, secret_key: str, paper_trading: bool = True):
        self.api_key = api_key
        self.secret_key = secret_key
        self.paper_trading = paper_trading
        self.base_url = "https://paper-api.alpaca.markets" if paper_trading else "https://api.alpaca.markets"
        self.data_url = "https://data.alpaca.markets"
        self.headers = {
            "APCA-API-KEY-ID": api_key,
            "APCA-API-SECRET-KEY": secret_key
        }
        self.logger = logging.getLogger(__name__)
    
    def get_account_info(self) -> Optional[Dict[str, Any]]:
        """Get account information"""
        try:
            url = f"{self.base_url}/v2/account"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            self.logger.error(f"Error fetching account info: {e}")
            return None
    
    def get_positions(self) -> List[Dict[str, Any]]:
        """Get current positions"""
        try:
            url = f"{self.base_url}/v2/positions"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            self.logger.error(f"Error fetching positions: {e}")
            return []
    
    def place_order(self, symbol: str, qty: int, side: str, 
                   order_type: str = "market") -> Optional[Dict[str, Any]]:
        """Place a trading order"""
        try:
            url = f"{self.base_url}/v2/orders"
            
            order_data = {
                "symbol": symbol,
                "qty": str(qty),
                "side": side,
                "type": order_type,
                "time_in_force": "day"
            }
            
            response = requests.post(url, headers=self.headers, json=order_data)
            response.raise_for_status()
            
            order_result = response.json()
            self.logger.info(f"Order placed: {side} {qty} {symbol}")
            return order_result
            
        except Exception as e:
            self.logger.error(f"Error placing order: {e}")
            return None
    
    def get_bars(self, symbol: str, timeframe: str = "1Min", 
                limit: int = 1000) -> List[MarketData]:
        """Get historical bars from Alpaca"""
        try:
            url = f"{self.data_url}/v2/stocks/{symbol}/bars"
            
            # Calculate start time (last few days)
            start_time = (datetime.now() - timedelta(days=5)).isoformat() + "Z"
            
            params = {
                "timeframe": timeframe,
                "start": start_time,
                "limit": limit,
                "adjustment": "raw",
                "feed": "iex"
            }
            
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            data = response.json()
            bars = data.get("bars", [])
            
            market_data = []
            for bar in bars:
                try:
                    timestamp = datetime.fromisoformat(bar["t"].replace("Z", "+00:00"))
                    market_data.append(MarketData(
                        timestamp=timestamp,
                        open=float(bar["o"]),
                        high=float(bar["h"]),
                        low=float(bar["l"]),
                        close=float(bar["c"]),
                        volume=int(bar["v"])
                    ))
                except (ValueError, KeyError) as e:
                    self.logger.warning(f"Error parsing bar: {e}")
                    continue
            
            self.logger.info(f"Retrieved {len(market_data)} bars from Alpaca")
            return market_data
            
        except Exception as e:
            self.logger.error(f"Error fetching bars from Alpaca: {e}")
            return []


class MarketDataManager:
    """Unified market data manager"""
    
    def __init__(self, fmp_api_key: str, alpaca_api_key: str, alpaca_secret: str):
        self.fmp_provider = FMPDataProvider(fmp_api_key)
        self.alpaca_provider = AlpacaDataProvider(alpaca_api_key, alpaca_secret)
        self.logger = logging.getLogger(__name__)
    
    def get_market_data(self, symbol: str, source: str = "fmp", 
                       timeframe: str = "1min", days: int = 5) -> List[MarketData]:
        """Get market data from specified source"""
        if source.lower() == "fmp":
            return self.fmp_provider.get_historical_data(symbol, timeframe, days)
        elif source.lower() == "alpaca":
            return self.alpaca_provider.get_bars(symbol, "1Min" if timeframe == "1min" else timeframe)
        else:
            self.logger.error(f"Unknown data source: {source}")
            return []
    
    def get_live_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get live quote"""
        return self.fmp_provider.get_real_time_quote(symbol)
