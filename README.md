# Automated Momentum Reversal Trading System

A comprehensive automated trading system that detects momentum reversal patterns and executes trades based on specific technical criteria.

## Features

### Pattern Detection Algorithm
- **Declining Red Momentum Phase**: Detects 4+ consecutive bars with declining negative momentum
- **Momentum Reversal Signal**: Identifies when momentum turns from red to yellow (negative to positive)
- **ATR Confirmation**: Validates signals using ATR trailing stop with blue signal confirmation

### Trading System
- **Automated Entry**: Generates buy signals when all three conditions are met simultaneously
- **Position Management**: Holds positions while either bar pattern OR ATR confirmation remains favorable
- **Smart Exit Logic**: Closes positions when both bar pattern AND ATR confirmation deteriorate
- **Risk Management**: Built-in position sizing and capital management

### System Architecture
- **Real-time Monitoring**: Continuously monitors market data for trading setups
- **Automated Execution**: Automatically executes trades when patterns are detected (via Alpaca API)
- **Performance Tracking**: Tracks success rates and trading performance
- **Backtesting Engine**: Test strategies on historical data

## Installation

1. Clone or download the repository
2. Install required dependencies:
```bash
pip install -r requirements.txt
```

## Configuration

The system uses your provided API keys:
- **Alpaca API Key**: PKUQBMURWJZ7IW64SE8A
- **Alpaca Secret**: dx331VMHCNncAf8NoiwoMhNjucFizl55xfP9YqOJ
- **FMP API Key**: K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7

Configuration is handled in `config.py` with the following key settings:
- Initial capital: $100,000
- Position size: 2% of capital per trade
- Momentum period: 14 bars
- ATR period: 14 bars
- Minimum declining bars: 4
- Paper trading: Enabled by default

## Usage

### Run Single Symbol Backtest
```bash
python main.py --mode backtest --symbol SPY
```

### Run Multiple Symbol Backtests
```bash
python main.py --mode multi-backtest
```

### Run Live Trading
```bash
python main.py --mode live --symbols SPY QQQ AAPL
```

### Custom Symbol List
```bash
python main.py --mode backtest --symbol TSLA
python main.py --mode live --symbols NVDA MSFT GOOGL
```

## System Components

### 1. Data Models (`data_models.py`)
- Market data structures
- Technical indicator containers
- Trading signal definitions
- Position and trade tracking
- Performance metrics

### 2. Technical Indicators (`technical_indicators.py`)
- Momentum calculation with color coding
- ATR trailing stop implementation
- Technical analysis engine

### 3. Pattern Detection (`pattern_detector.py`)
- Momentum reversal pattern detection
- Multi-criteria signal validation
- Pattern strength scoring

### 4. Trading Engine (`trading_engine.py`)
- Position management
- Order execution logic
- Performance tracking
- Risk management

### 5. Market Data Provider (`market_data_provider.py`)
- FMP API integration for historical data
- Alpaca API integration for live trading
- Real-time quote handling

### 6. Main System (`automated_trading_system.py`)
- Orchestrates all components
- Backtesting engine
- Live trading execution
- Results analysis and export

## Pattern Detection Logic

### Entry Conditions (ALL must be true):
1. **Declining Red Momentum**: 4+ consecutive bars with:
   - Declining momentum (each bar < previous bar)
   - Red color (negative momentum)

2. **Momentum Reversal**: 
   - Current bar turns yellow (positive momentum)
   - Previous bar was red (negative momentum)

3. **ATR Confirmation**:
   - ATR confirmed = true
   - ATR signal = blue (uptrend confirmation)

### Exit Conditions (BOTH must be true):
1. **Bar Pattern Deterioration**: No recent favorable pattern characteristics
2. **ATR Confirmation Loss**: ATR signal no longer blue OR not confirmed

## Output and Results

### Backtest Results Include:
- Signal statistics (entry/exit signals, pattern detections)
- Trading performance (win rate, P&L, returns)
- Pattern success rates
- Detailed signal analysis
- Momentum and ATR statistics

### Live Trading Features:
- Real-time signal detection
- Automated order execution
- Position monitoring
- Performance tracking
- Comprehensive logging

### Export Capabilities:
- JSON results export
- Detailed trade logs
- Performance summaries
- Signal analysis data

## Risk Management

- **Position Sizing**: 2% of capital per trade
- **Paper Trading**: Enabled by default for safety
- **Stop Loss**: Integrated via ATR trailing stop
- **Maximum Positions**: 1 concurrent position
- **Capital Protection**: Prevents over-leveraging

## Logging and Monitoring

- Comprehensive logging to file and console
- Real-time status monitoring
- Error handling and recovery
- Performance metrics tracking

## Example Output

```
RUNNING BACKTEST FOR SPY
============================================================
Backtest Results for SPY:
Period: 2024-01-15T09:30:00 to 2024-01-20T16:00:00
Total Bars Processed: 1950

Signal Statistics:
  Entry Signals: 12
  Exit Signals: 11
  Declining Red Momentum Detected: 45
  Momentum Reversals Detected: 23
  ATR Confirmations: 67

Trading Performance:
  Total Trades: 11
  Win Rate: 63.64%
  Total P&L: $2,450.00
  Total Return: 2.45%
  Average Win: $520.00
  Average Loss: -$180.00
  Final Capital: $102,450.00

Pattern Success Rate:
  Success Rate: 63.64%
  Successful Patterns: 7
  Total Patterns: 11
```

## Safety Features

- **Paper Trading**: All trades execute in paper trading mode by default
- **Configuration Validation**: Validates all settings before execution
- **Error Handling**: Comprehensive error handling and logging
- **Position Limits**: Prevents excessive position sizes
- **API Rate Limiting**: Respects API rate limits

## Customization

You can customize the system by modifying `config.py`:
- Trading parameters (position size, periods)
- Risk management settings
- Symbol lists
- API configurations
- System behavior

## Support and Troubleshooting

Check the log files for detailed information about system operation and any errors. The system includes comprehensive logging and error handling to help diagnose issues.

For optimal performance:
1. Ensure stable internet connection for live trading
2. Monitor API rate limits
3. Review backtest results before live trading
4. Start with paper trading to validate performance

## Disclaimer

This is an automated trading system that involves financial risk. Always:
- Test thoroughly with paper trading first
- Understand the risks involved
- Monitor system performance
- Use appropriate position sizing
- Consider market conditions

Past performance does not guarantee future results.
