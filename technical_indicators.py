"""
Technical indicators for the automated trading system.
"""
import numpy as np
from typing import List, Tuple
from data_models import MarketData, MomentumColor, ATRSignal


class MomentumIndicator:
    """Momentum indicator with color coding"""
    
    def __init__(self, period: int = 14):
        self.period = period
        self.momentum_history: List[float] = []
    
    def calculate_momentum(self, prices: List[float]) -> float:
        """Calculate momentum as rate of change"""
        if len(prices) < self.period + 1:
            return 0.0
        
        current_price = prices[-1]
        past_price = prices[-(self.period + 1)]
        
        if past_price == 0:
            return 0.0
            
        momentum = ((current_price - past_price) / past_price) * 100
        return momentum
    
    def get_momentum_color(self, momentum: float) -> MomentumColor:
        """Determine momentum color based on value"""
        if momentum > 0.5:
            return MomentumColor.GREEN
        elif momentum > -0.5:
            return MomentumColor.YELLOW
        else:
            return MomentumColor.RED
    
    def is_declining_momentum(self, current_momentum: float) -> bool:
        """Check if momentum is declining compared to previous value"""
        if not self.momentum_history:
            return False
        
        return current_momentum < self.momentum_history[-1]
    
    def update(self, momentum: float):
        """Update momentum history"""
        self.momentum_history.append(momentum)
        # Keep only last 20 values for efficiency
        if len(self.momentum_history) > 20:
            self.momentum_history.pop(0)


class ATRTrailingStop:
    """ATR-based trailing stop indicator"""
    
    def __init__(self, atr_period: int = 14, multiplier: float = 2.0):
        self.atr_period = atr_period
        self.multiplier = multiplier
        self.atr_values: List[float] = []
        self.trailing_stop: float = 0.0
        self.trend_direction: str = "neutral"
    
    def calculate_true_range(self, high: float, low: float, prev_close: float) -> float:
        """Calculate True Range for ATR"""
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        return max(tr1, tr2, tr3)
    
    def calculate_atr(self, market_data: List[MarketData]) -> float:
        """Calculate Average True Range"""
        if len(market_data) < 2:
            return 0.0
        
        true_ranges = []
        for i in range(1, len(market_data)):
            current = market_data[i]
            previous = market_data[i-1]
            tr = self.calculate_true_range(current.high, current.low, previous.close)
            true_ranges.append(tr)
        
        if len(true_ranges) < self.atr_period:
            return np.mean(true_ranges) if true_ranges else 0.0
        
        # Use exponential moving average for ATR
        return np.mean(true_ranges[-self.atr_period:])
    
    def update_trailing_stop(self, price: float, atr: float) -> Tuple[float, ATRSignal, bool]:
        """Update trailing stop and determine signal"""
        if atr == 0:
            return self.trailing_stop, ATRSignal.NEUTRAL, False
        
        # Calculate upper and lower bands
        upper_band = price + (atr * self.multiplier)
        lower_band = price - (atr * self.multiplier)
        
        # Update trailing stop logic
        if self.trend_direction == "up":
            self.trailing_stop = max(self.trailing_stop, lower_band)
            if price <= self.trailing_stop:
                self.trend_direction = "down"
                self.trailing_stop = upper_band
        elif self.trend_direction == "down":
            self.trailing_stop = min(self.trailing_stop, upper_band)
            if price >= self.trailing_stop:
                self.trend_direction = "up"
                self.trailing_stop = lower_band
        else:  # neutral
            if price > (upper_band + lower_band) / 2:
                self.trend_direction = "up"
                self.trailing_stop = lower_band
            else:
                self.trend_direction = "down"
                self.trailing_stop = upper_band
        
        # Determine signal and confirmation
        if self.trend_direction == "up":
            signal = ATRSignal.BLUE
            confirmed = price > self.trailing_stop
        else:
            signal = ATRSignal.RED
            confirmed = price < self.trailing_stop
        
        return self.trailing_stop, signal, confirmed


class TechnicalAnalyzer:
    """Main technical analysis engine"""
    
    def __init__(self, momentum_period: int = 14, atr_period: int = 14, atr_multiplier: float = 2.0):
        self.momentum_indicator = MomentumIndicator(momentum_period)
        self.atr_trailing_stop = ATRTrailingStop(atr_period, atr_multiplier)
        self.market_history: List[MarketData] = []
    
    def analyze(self, market_data: MarketData) -> dict:
        """Perform complete technical analysis on new market data"""
        self.market_history.append(market_data)

        # Keep only necessary history for efficiency
        if len(self.market_history) > 100:
            self.market_history.pop(0)

        # Calculate momentum
        prices = [data.close for data in self.market_history]
        momentum = self.momentum_indicator.calculate_momentum(prices)
        momentum_color = self.momentum_indicator.get_momentum_color(momentum)
        is_declining = self.momentum_indicator.is_declining_momentum(momentum)

        # Update momentum history
        self.momentum_indicator.update(momentum)

        # Calculate ATR and trailing stop
        atr_value = self.atr_trailing_stop.calculate_atr(self.market_history)

        # Ensure we have valid ATR value
        if atr_value == 0 and len(self.market_history) >= 2:
            # Calculate simple volatility as fallback
            recent_prices = [data.close for data in self.market_history[-10:]]
            if len(recent_prices) > 1:
                price_changes = [abs(recent_prices[i] - recent_prices[i-1])
                               for i in range(1, len(recent_prices))]
                atr_value = sum(price_changes) / len(price_changes) if price_changes else 0.1

        trailing_stop, atr_signal, atr_confirmed = self.atr_trailing_stop.update_trailing_stop(
            market_data.close, atr_value
        )

        return {
            'momentum': momentum,
            'momentum_color': momentum_color,
            'is_declining_momentum': is_declining,
            'atr_value': atr_value,
            'atr_signal': atr_signal,
            'atr_confirmed': atr_confirmed,
            'trailing_stop': trailing_stop
        }
