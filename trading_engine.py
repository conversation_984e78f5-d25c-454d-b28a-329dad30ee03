"""
Trading engine for automated position management and execution.
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
import logging
from data_models import (
    Position, Trade, PositionStatus, TradingSignal, 
    MarketData, PerformanceMetrics
)


class TradingEngine:
    """Manages trading positions and executes buy/sell orders"""
    
    def __init__(self, initial_capital: float = 100000.0, position_size: float = 0.02):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.position_size = position_size  # 2% of capital per trade
        self.current_position: Optional[Position] = None
        self.trades: List[Trade] = []
        self.performance_metrics = PerformanceMetrics()
        self.logger = logging.getLogger(__name__)
        
    def calculate_position_size(self, entry_price: float) -> int:
        """Calculate position size based on available capital"""
        trade_amount = self.current_capital * self.position_size
        shares = int(trade_amount / entry_price)
        return max(1, shares)  # Minimum 1 share
    
    def can_enter_position(self) -> bool:
        """Check if we can enter a new position"""
        return self.current_position is None
    
    def enter_position(self, signal: TradingSignal) -> bool:
        """Enter a new long position"""
        if not self.can_enter_position():
            self.logger.warning("Cannot enter position - already in position")
            return False
        
        if not signal.pattern.entry_signal:
            self.logger.warning("Cannot enter position - no entry signal")
            return False
        
        entry_price = signal.market_data.close
        quantity = self.calculate_position_size(entry_price)
        
        if quantity * entry_price > self.current_capital:
            self.logger.warning("Insufficient capital for position")
            return False
        
        self.current_position = Position(
            entry_timestamp=signal.timestamp,
            entry_price=entry_price,
            quantity=quantity,
            current_price=entry_price,
            unrealized_pnl=0.0
        )
        
        # Update capital
        self.current_capital -= quantity * entry_price
        
        self.logger.info(f"Entered position: {quantity} shares at ${entry_price:.2f}")
        return True
    
    def exit_position(self, signal: TradingSignal) -> bool:
        """Exit current position"""
        if self.current_position is None:
            self.logger.warning("Cannot exit position - no active position")
            return False
        
        exit_price = signal.market_data.close
        position = self.current_position
        
        # Calculate P&L
        pnl = (exit_price - position.entry_price) * position.quantity
        
        # Create trade record
        duration_bars = self._calculate_duration_bars(
            position.entry_timestamp, signal.timestamp
        )
        
        trade = Trade(
            entry_timestamp=position.entry_timestamp,
            exit_timestamp=signal.timestamp,
            entry_price=position.entry_price,
            exit_price=exit_price,
            quantity=position.quantity,
            pnl=pnl,
            duration_bars=duration_bars,
            pattern_type="momentum_reversal"
        )
        
        self.trades.append(trade)
        
        # Update capital
        self.current_capital += position.quantity * exit_price
        
        self.logger.info(
            f"Exited position: {position.quantity} shares at ${exit_price:.2f}, "
            f"P&L: ${pnl:.2f}"
        )
        
        # Clear current position
        self.current_position = None
        
        # Update performance metrics
        self.performance_metrics.update_metrics(self.trades)
        
        return True
    
    def update_position(self, current_price: float):
        """Update current position with latest price"""
        if self.current_position:
            self.current_position.update_current_price(current_price)
    
    def process_signal(self, signal: TradingSignal) -> PositionStatus:
        """Process trading signal and execute trades"""
        current_price = signal.market_data.close
        
        # Update existing position
        if self.current_position:
            self.update_position(current_price)
        
        # Handle entry signals
        if signal.pattern.entry_signal and self.can_enter_position():
            if self.enter_position(signal):
                return PositionStatus.IN
        
        # Handle exit signals
        if signal.pattern.exit_signal and self.current_position:
            if self.exit_position(signal):
                return PositionStatus.OUT
        
        # Return current status
        return PositionStatus.IN if self.current_position else PositionStatus.OUT
    
    def _calculate_duration_bars(self, entry_time: datetime, exit_time: datetime) -> int:
        """Calculate duration in bars (simplified - assumes 1 minute bars)"""
        duration = exit_time - entry_time
        return int(duration.total_seconds() / 60)  # Convert to minutes
    
    def get_current_status(self) -> Dict[str, Any]:
        """Get current trading status"""
        status = {
            'in_position': self.current_position is not None,
            'current_capital': self.current_capital,
            'total_trades': len(self.trades),
            'total_pnl': sum(trade.pnl for trade in self.trades),
            'win_rate': self.performance_metrics.win_rate,
        }
        
        if self.current_position:
            status.update({
                'position_entry_price': self.current_position.entry_price,
                'position_quantity': self.current_position.quantity,
                'position_current_price': self.current_position.current_price,
                'unrealized_pnl': self.current_position.unrealized_pnl
            })
        
        return status
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get detailed performance summary"""
        if not self.trades:
            return {'message': 'No trades executed yet'}
        
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital * 100
        
        return {
            'total_trades': self.performance_metrics.total_trades,
            'winning_trades': self.performance_metrics.winning_trades,
            'losing_trades': self.performance_metrics.losing_trades,
            'win_rate': f"{self.performance_metrics.win_rate:.2%}",
            'total_pnl': f"${self.performance_metrics.total_pnl:.2f}",
            'total_return': f"{total_return:.2%}",
            'avg_win': f"${self.performance_metrics.avg_win:.2f}",
            'avg_loss': f"${self.performance_metrics.avg_loss:.2f}",
            'current_capital': f"${self.current_capital:.2f}",
            'initial_capital': f"${self.initial_capital:.2f}"
        }
    
    def reset(self):
        """Reset trading engine to initial state"""
        self.current_capital = self.initial_capital
        self.current_position = None
        self.trades.clear()
        self.performance_metrics = PerformanceMetrics()
        self.logger.info("Trading engine reset to initial state")
