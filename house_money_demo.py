"""
Demo script showcasing the House Money Strategy for daily profit targets.
"""
from datetime import datetime, timedelta
import numpy as np
from data_models import MarketData, TechnicalIndicators, TradingSignal, PatternSignal, PositionStatus, MomentumColor, ATRSignal
from trading_engine import TradingEngine
from config import TRADING_CONFIG


def create_profitable_trading_scenario():
    """Create a scenario with multiple profitable trades to demonstrate house money strategy"""
    print("Creating profitable trading scenario...")
    
    base_time = datetime.now() - timedelta(hours=3)
    scenarios = []
    
    # Scenario 1: Small winner (+$15)
    print("  Scenario 1: Small winner (+$15)")
    entry_price = 100.0
    exit_price = 101.5
    scenarios.append({
        'entry_time': base_time + timedelta(minutes=10),
        'exit_time': base_time + timedelta(minutes=25),
        'entry_price': entry_price,
        'exit_price': exit_price,
        'expected_pnl': 15.0  # Approximate
    })
    
    # Scenario 2: Medium winner (+$35) - This should trigger profit lock
    print("  Scenario 2: Medium winner (+$35) - Should trigger profit lock")
    entry_price = 102.0
    exit_price = 105.5
    scenarios.append({
        'entry_time': base_time + timedelta(minutes=45),
        'exit_time': base_time + timedelta(minutes=70),
        'entry_price': entry_price,
        'exit_price': exit_price,
        'expected_pnl': 35.0  # Approximate
    })
    
    # Scenario 3: House money trade (+$25) - Playing with profits
    print("  Scenario 3: House money trade (+$25) - Playing with profits")
    entry_price = 106.0
    exit_price = 108.0
    scenarios.append({
        'entry_time': base_time + timedelta(minutes=90),
        'exit_time': base_time + timedelta(minutes=110),
        'entry_price': entry_price,
        'exit_price': exit_price,
        'expected_pnl': 25.0  # Approximate
    })
    
    # Scenario 4: Final push to target (+$30) - Should reach $100 target
    print("  Scenario 4: Final push to target (+$30) - Should reach $100 target")
    entry_price = 108.5
    exit_price = 111.0
    scenarios.append({
        'entry_time': base_time + timedelta(minutes=130),
        'exit_time': base_time + timedelta(minutes=150),
        'entry_price': entry_price,
        'exit_price': exit_price,
        'expected_pnl': 30.0  # Approximate
    })
    
    return scenarios


def create_trading_signal(timestamp: datetime, price: float, is_entry: bool = True) -> TradingSignal:
    """Create a trading signal for the demo"""
    market_data = MarketData(
        timestamp=timestamp,
        open=price,
        high=price + 0.5,
        low=price - 0.5,
        close=price,
        volume=1000
    )
    
    indicators = TechnicalIndicators(
        momentum=2.0 if is_entry else -1.0,
        momentum_color=MomentumColor.YELLOW if is_entry else MomentumColor.RED,
        atr_value=1.5,
        atr_signal=ATRSignal.BLUE if is_entry else ATRSignal.RED,
        atr_confirmed=True
    )
    
    pattern = PatternSignal(
        declining_red_momentum=True,
        momentum_reversal=is_entry,
        atr_confirmation=is_entry,
        bar_pattern_detected=True,
        entry_signal=is_entry,
        exit_signal=not is_entry
    )
    
    return TradingSignal(
        timestamp=timestamp,
        market_data=market_data,
        indicators=indicators,
        pattern=pattern,
        position_status=PositionStatus.OUT
    )


def demonstrate_house_money_strategy():
    """Demonstrate the house money strategy in action"""
    print("=" * 70)
    print("HOUSE MONEY STRATEGY DEMONSTRATION")
    print("=" * 70)
    print(f"Daily Target: ${TRADING_CONFIG.daily_profit_target:.2f}")
    print(f"Initial Profit Lock: ${TRADING_CONFIG.initial_profit_lock:.2f}")
    print(f"House Money Multiplier: {TRADING_CONFIG.house_money_multiplier:.1f}x")
    print(f"Profit Protection: {TRADING_CONFIG.profit_protection_percent:.0%}")
    print("=" * 70)
    
    # Initialize trading engine with house money strategy
    engine = TradingEngine(initial_capital=10000, position_size=0.02)  # 2% position size
    
    # Create profitable scenarios
    scenarios = create_profitable_trading_scenario()
    
    print(f"\nStarting with ${engine.current_capital:,.2f}")
    print(f"Position size: {engine.position_size:.1%} of capital")
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n" + "─" * 50)
        print(f"TRADE {i}: Expected P&L ~${scenario['expected_pnl']:.2f}")
        print("─" * 50)
        
        # Get status before trade
        status_before = engine.get_current_status()
        print(f"Before Trade:")
        print(f"  Daily P&L: ${status_before['daily_pnl']:.2f}")
        print(f"  Trading Phase: {status_before['trading_phase']}")
        print(f"  Position Multiplier: {status_before['position_multiplier']:.1f}x")
        print(f"  Locked Profit: ${status_before['locked_profit']:.2f}")
        
        # Entry signal
        entry_signal = create_trading_signal(scenario['entry_time'], scenario['entry_price'], is_entry=True)
        entry_success = engine.enter_position(entry_signal)
        
        if not entry_success:
            print("  ❌ Entry rejected!")
            continue
        
        print(f"  ✅ Entered at ${scenario['entry_price']:.2f}")
        
        # Update position with exit price
        engine.update_position(scenario['exit_price'])
        
        # Exit signal
        exit_signal = create_trading_signal(scenario['exit_time'], scenario['exit_price'], is_entry=False)
        exit_success = engine.exit_position(exit_signal)
        
        if exit_success:
            print(f"  ✅ Exited at ${scenario['exit_price']:.2f}")
        
        # Get status after trade
        status_after = engine.get_current_status()
        print(f"\nAfter Trade:")
        print(f"  Daily P&L: ${status_after['daily_pnl']:.2f}")
        print(f"  Progress: {status_after['progress_percent']:.1f}%")
        print(f"  Trading Phase: {status_after['trading_phase']}")
        
        if status_after['locked_profit'] > 0:
            print(f"  💰 Locked Profit: ${status_after['locked_profit']:.2f}")
            print(f"  🏠 House Money Available: ${status_after['house_money_available']:.2f}")
        
        if status_after['target_reached']:
            print(f"  🎉 DAILY TARGET REACHED!")
            break
    
    # Final summary
    print(f"\n" + "=" * 70)
    print("FINAL HOUSE MONEY STRATEGY RESULTS")
    print("=" * 70)
    
    final_status = engine.get_current_status()
    performance = engine.get_performance_summary()
    
    print(f"Daily P&L: ${final_status['daily_pnl']:.2f}")
    print(f"Daily Target: ${final_status['daily_target']:.2f}")
    print(f"Progress: {final_status['progress_percent']:.1f}%")
    print(f"Target Reached: {'✅ YES' if final_status['target_reached'] else '❌ NO'}")
    print(f"Locked Profit: ${final_status['locked_profit']:.2f}")
    print(f"House Money Trades: {final_status.get('house_money_trades', 0)}")
    
    print(f"\nTrading Performance:")
    if 'total_trades' in performance and performance['total_trades'] != 'No trades executed yet':
        print(f"  Total Trades: {performance['total_trades']}")
        print(f"  Win Rate: {performance['win_rate']}")
        print(f"  Total P&L: {performance['total_pnl']}")
        print(f"  Final Capital: {performance['current_capital']}")
    
    # Show the house money strategy phases
    print(f"\n" + "─" * 50)
    print("HOUSE MONEY STRATEGY PHASES DEMONSTRATED:")
    print("─" * 50)
    print("✅ Phase 1: INITIAL_CAPITAL - Conservative trading")
    print("✅ Phase 2: PROFIT_LOCK - Lock in initial profits")
    print("✅ Phase 3: HOUSE_MONEY - Aggressive trading with profits")
    print("✅ Phase 4: TARGET_REACHED - Stop trading for the day")
    
    # Log daily summary
    engine.house_money_manager.log_daily_summary()


def demonstrate_risk_management():
    """Demonstrate risk management features of house money strategy"""
    print(f"\n" + "=" * 70)
    print("HOUSE MONEY RISK MANAGEMENT FEATURES")
    print("=" * 70)
    
    print("🛡️  PROFIT PROTECTION:")
    print(f"   • Lock initial profit after ${TRADING_CONFIG.initial_profit_lock:.2f}")
    print(f"   • Protect {TRADING_CONFIG.profit_protection_percent:.0%} of additional profits")
    print(f"   • Never risk more than {TRADING_CONFIG.max_house_money_risk:.0%} of total capital")
    
    print(f"\n💰 POSITION SIZING:")
    print(f"   • Initial phase: Normal position size")
    print(f"   • House money phase: {TRADING_CONFIG.house_money_multiplier:.1f}x position size")
    print(f"   • Target reached: Stop trading (0x position size)")
    
    print(f"\n🎯 DAILY TARGET SYSTEM:")
    print(f"   • Daily target: ${TRADING_CONFIG.daily_profit_target:.2f}")
    print(f"   • Stop trading when target reached")
    print(f"   • Reset each trading day")
    
    print(f"\n🏠 HOUSE MONEY PHILOSOPHY:")
    print("   • Take profit first (secure initial gains)")
    print("   • Play with house money (use profits for bigger positions)")
    print("   • Protect the base (never risk original capital)")
    print("   • Reach daily target (systematic profit taking)")


def main():
    """Main demonstration function"""
    print("AUTOMATED TRADING SYSTEM")
    print("House Money Strategy Demo")
    print("Take profit, then play with house money to reach $100/day")
    
    try:
        # Show risk management features
        demonstrate_risk_management()
        
        # Run the main demonstration
        demonstrate_house_money_strategy()
        
        print(f"\n" + "=" * 70)
        print("HOUSE MONEY STRATEGY DEMO COMPLETED!")
        print("=" * 70)
        
        print("\n🎯 Key Benefits Demonstrated:")
        print("✅ Systematic profit taking and protection")
        print("✅ Risk-adjusted position sizing")
        print("✅ Daily target achievement")
        print("✅ House money utilization")
        print("✅ Capital preservation")
        
        print(f"\nTo run with real trading:")
        print(f"  python main.py --mode backtest --symbol SPY")
        print(f"  python main.py --mode live --symbols SPY QQQ")
        
    except Exception as e:
        print(f"Error during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
