"""
Command-line interface for the mass scanner with live updates.
"""
import time
import os
import sys
from datetime import datetime
from typing import List
import threading

from stock_universe import StockUniverse
from mass_scanner import MassScanner, ScanR<PERSON>ult
from config import TRADING_CONFIG


class ScannerCLI:
    """Command-line interface for mass scanning"""
    
    def __init__(self):
        self.universe = []
        self.scan_results = []
        self.is_scanning = False
        
    def clear_screen(self):
        """Clear the terminal screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def print_header(self):
        """Print application header"""
        print("=" * 80)
        print("🎯 MOMENTUM REVERSAL TRADING SYSTEM")
        print("S&P 500 + $100B Market Cap Scanner")
        print("=" * 80)
        print(f"Daily Target: ${TRADING_CONFIG.daily_profit_target}")
        print(f"Initial Profit Lock: ${TRADING_CONFIG.initial_profit_lock}")
        print(f"House Money Multiplier: {TRADING_CONFIG.house_money_multiplier}x")
        print("=" * 80)
    
    def load_universe(self):
        """Load stock universe"""
        print("📊 Loading stock universe...")
        universe_manager = StockUniverse()
        self.universe = universe_manager.build_universe()
        print(f"✅ Loaded {len(self.universe)} stocks for scanning")
        return len(self.universe)
    
    def display_scan_progress(self, current: int, total: int, batch_num: int, total_batches: int):
        """Display scan progress"""
        progress = (current / total) * 100 if total > 0 else 0
        bar_length = 50
        filled_length = int(bar_length * progress / 100)
        bar = '█' * filled_length + '-' * (bar_length - filled_length)
        
        print(f"\r🔍 Scanning: [{bar}] {progress:.1f}% ({current}/{total}) - Batch {batch_num}/{total_batches}", end='')
        sys.stdout.flush()
    
    def display_results_summary(self, results: List[ScanResult]):
        """Display scan results summary"""
        print("\n\n" + "=" * 80)
        print("📊 SCAN RESULTS SUMMARY")
        print("=" * 80)
        
        successful = [r for r in results if r.success]
        failed = [r for r in results if not r.success]
        
        entry_signals = [r for r in successful if r.entry_signals > 0]
        momentum_reversals = [r for r in successful if r.momentum_reversals > 0]
        declining_momentum = [r for r in successful if r.declining_red_momentum > 0]
        atr_confirmations = [r for r in successful if r.atr_confirmations > 0]
        
        print(f"Total Stocks Scanned: {len(results)}")
        print(f"Successful Scans: {len(successful)} ({len(successful)/len(results)*100:.1f}%)")
        print(f"Failed Scans: {len(failed)}")
        
        print(f"\n🎯 Pattern Detection:")
        print(f"  Entry Signals Found: {len(entry_signals)}")
        print(f"  Momentum Reversals: {len(momentum_reversals)}")
        print(f"  Declining Momentum: {len(declining_momentum)}")
        print(f"  ATR Confirmations: {len(atr_confirmations)}")
        
        return entry_signals, momentum_reversals
    
    def display_top_opportunities(self, results: List[ScanResult]):
        """Display top trading opportunities"""
        opportunities = [r for r in results if r.success and r.entry_signals > 0]
        
        if opportunities:
            print(f"\n🏆 TOP TRADING OPPORTUNITIES:")
            print("-" * 80)
            print(f"{'Symbol':<8} {'Signals':<8} {'Reversals':<10} {'ATR Conf':<9} {'Strength':<10} {'Price':<10}")
            print("-" * 80)
            
            # Sort by entry signals, then pattern strength
            opportunities.sort(key=lambda x: (x.entry_signals, x.pattern_strength), reverse=True)
            
            for opp in opportunities[:15]:  # Show top 15
                print(f"{opp.symbol:<8} {opp.entry_signals:<8} {opp.momentum_reversals:<10} "
                      f"{opp.atr_confirmations:<9} {opp.pattern_strength:<10.2f} ${opp.current_price:<9.2f}")
        else:
            print(f"\n📊 No entry signals found in current market conditions")
            print(f"The system is waiting for high-probability momentum reversal setups")
    
    def display_watch_list(self, results: List[ScanResult]):
        """Display stocks to watch (momentum reversals without full entry signals)"""
        watch_stocks = [r for r in results if r.success and r.momentum_reversals > 0 and r.entry_signals == 0]
        
        if watch_stocks:
            print(f"\n👀 WATCH LIST (Momentum Reversals - Potential Setups):")
            print("-" * 80)
            print(f"{'Symbol':<8} {'Reversals':<10} {'ATR Conf':<9} {'Strength':<10} {'Price':<10}")
            print("-" * 80)
            
            watch_stocks.sort(key=lambda x: x.momentum_reversals, reverse=True)
            
            for stock in watch_stocks[:10]:  # Show top 10
                print(f"{stock.symbol:<8} {stock.momentum_reversals:<10} {stock.atr_confirmations:<9} "
                      f"{stock.pattern_strength:<10.2f} ${stock.current_price:<9.2f}")
    
    def run_scan(self):
        """Run the mass scan"""
        if not self.universe:
            print("❌ No stocks loaded. Please load universe first.")
            return
        
        print(f"\n🚀 Starting mass scan of {len(self.universe)} stocks...")
        print(f"Using your API keys: Alpaca + FMP")
        
        scanner = MassScanner(max_workers=6)  # Conservative for CLI
        
        # Create batches
        batch_size = 25
        batches = [self.universe[i:i+batch_size] for i in range(0, len(self.universe), batch_size)]
        
        all_results = []
        start_time = time.time()
        
        for i, batch in enumerate(batches):
            if not self.is_scanning:
                break
            
            # Update progress
            current_stock = i * batch_size
            self.display_scan_progress(current_stock, len(self.universe), i+1, len(batches))
            
            # Scan batch
            batch_results = scanner.scan_batch(batch)
            all_results.extend(batch_results)
            
            # Brief pause
            time.sleep(0.5)
        
        # Final progress update
        self.display_scan_progress(len(self.universe), len(self.universe), len(batches), len(batches))
        
        scan_time = time.time() - start_time
        print(f"\n✅ Scan completed in {scan_time:.1f} seconds")
        
        self.scan_results = all_results
        return all_results
    
    def interactive_menu(self):
        """Interactive menu system"""
        while True:
            self.clear_screen()
            self.print_header()
            
            if self.universe:
                print(f"📊 Universe: {len(self.universe)} stocks loaded")
            else:
                print("📊 Universe: Not loaded")
            
            if self.scan_results:
                successful = sum(1 for r in self.scan_results if r.success)
                entry_signals = sum(1 for r in self.scan_results if r.success and r.entry_signals > 0)
                print(f"🔍 Last Scan: {successful} stocks scanned, {entry_signals} with entry signals")
            
            print(f"\n📋 MENU:")
            print(f"1. Load Stock Universe (S&P 500 + $100B Market Cap)")
            print(f"2. Run Mass Scan")
            print(f"3. View Scan Results")
            print(f"4. View Top Opportunities")
            print(f"5. View Watch List")
            print(f"6. Start Live Trading (GUI)")
            print(f"7. Exit")
            
            choice = input(f"\nSelect option (1-7): ").strip()
            
            if choice == '1':
                self.load_universe()
                input("\nPress Enter to continue...")
            
            elif choice == '2':
                if not self.universe:
                    print("❌ Please load universe first (option 1)")
                    input("Press Enter to continue...")
                else:
                    self.is_scanning = True
                    results = self.run_scan()
                    if results:
                        entry_signals, momentum_reversals = self.display_results_summary(results)
                        self.display_top_opportunities(results)
                    input("\nPress Enter to continue...")
            
            elif choice == '3':
                if not self.scan_results:
                    print("❌ No scan results available. Run a scan first (option 2)")
                else:
                    self.display_results_summary(self.scan_results)
                input("\nPress Enter to continue...")
            
            elif choice == '4':
                if not self.scan_results:
                    print("❌ No scan results available. Run a scan first (option 2)")
                else:
                    self.display_top_opportunities(self.scan_results)
                input("\nPress Enter to continue...")
            
            elif choice == '5':
                if not self.scan_results:
                    print("❌ No scan results available. Run a scan first (option 2)")
                else:
                    self.display_watch_list(self.scan_results)
                input("\nPress Enter to continue...")
            
            elif choice == '6':
                print("🖥️  Starting GUI application...")
                print("Note: The GUI will open in a separate window")
                os.system("python trading_gui.py")
            
            elif choice == '7':
                print("👋 Goodbye!")
                break
            
            else:
                print("❌ Invalid option. Please try again.")
                input("Press Enter to continue...")
    
    def run_continuous_scan(self):
        """Run continuous scanning mode"""
        print("🔄 Starting continuous scanning mode...")
        print("Press Ctrl+C to stop")
        
        try:
            while True:
                print(f"\n⏰ {datetime.now().strftime('%H:%M:%S')} - Running scan...")
                
                results = self.run_scan()
                if results:
                    entry_signals, _ = self.display_results_summary(results)
                    self.display_top_opportunities(results)
                
                print(f"\n⏳ Waiting 5 minutes before next scan...")
                time.sleep(300)  # Wait 5 minutes
                
        except KeyboardInterrupt:
            print(f"\n🛑 Continuous scanning stopped")


def main():
    """Main CLI entry point"""
    cli = ScannerCLI()
    
    if len(sys.argv) > 1 and sys.argv[1] == '--continuous':
        # Load universe and start continuous scanning
        cli.load_universe()
        cli.run_continuous_scan()
    else:
        # Interactive menu
        cli.interactive_menu()


if __name__ == "__main__":
    main()
