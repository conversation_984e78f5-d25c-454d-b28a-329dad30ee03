"""
Enhanced GUI for Continuous Real-Time Scanner with Progressive Pattern Monitoring
"""
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
from datetime import datetime
import queue

from continuous_scanner import ContinuousScanner
from progressive_pattern_monitor import PatternStage
from config import TRADING_CONFIG


class ContinuousScannerGUI:
    """GUI for continuous real-time scanning system"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Continuous Momentum Reversal Scanner - Real-Time Pattern Monitoring")
        self.root.geometry("1600x1000")
        
        # Scanner and threading
        self.scanner = None
        self.scanner_thread = None
        self.is_scanning = False
        self.message_queue = queue.Queue()
        
        # Create GUI
        self.create_widgets()
        self.process_messages()
        
        # Initialize scanner
        self.initialize_scanner()
    
    def create_widgets(self):
        """Create GUI widgets"""
        # Title and status
        title_frame = tk.Frame(self.root)
        title_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(title_frame, text="🔄 Continuous Momentum Reversal Scanner", 
                font=('Arial', 16, 'bold')).pack(side='left')
        
        self.market_status = tk.Label(title_frame, text="Market: Checking...", 
                                     font=('Arial', 12), fg='orange')
        self.market_status.pack(side='right')
        
        # Control panel
        control_frame = tk.LabelFrame(self.root, text="Scanner Controls", 
                                     font=('Arial', 12, 'bold'))
        control_frame.pack(fill='x', padx=10, pady=5)
        
        button_frame = tk.Frame(control_frame)
        button_frame.pack(fill='x', padx=10, pady=5)
        
        self.start_button = tk.Button(button_frame, text="🚀 Start Continuous Scanning", 
                                     command=self.start_scanning, font=('Arial', 11, 'bold'),
                                     bg='#4CAF50', fg='white')
        self.start_button.pack(side='left', padx=5)
        
        self.stop_button = tk.Button(button_frame, text="⏹ Stop Scanning", 
                                    command=self.stop_scanning, font=('Arial', 11, 'bold'),
                                    bg='#f44336', fg='white', state='disabled')
        self.stop_button.pack(side='left', padx=5)
        
        # Status indicators
        status_frame = tk.Frame(control_frame)
        status_frame.pack(fill='x', padx=10, pady=5)
        
        self.scan_status = tk.Label(status_frame, text="Status: Ready", font=('Arial', 11))
        self.scan_status.pack(side='left')
        
        self.scan_count_label = tk.Label(status_frame, text="Scans: 0", font=('Arial', 11))
        self.scan_count_label.pack(side='right')
        
        # Main content area
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Left panel - Watch List and Statistics
        left_panel = tk.Frame(main_frame)
        left_panel.pack(side='left', fill='both', expand=True, padx=(0, 5))
        
        self.create_watch_list_panel(left_panel)
        self.create_statistics_panel(left_panel)
        
        # Right panel - Entry Signals and Log
        right_panel = tk.Frame(main_frame)
        right_panel.pack(side='right', fill='both', expand=True, padx=(5, 0))
        
        self.create_entry_signals_panel(right_panel)
        self.create_log_panel(right_panel)
    
    def create_watch_list_panel(self, parent):
        """Create watch list panel showing pattern progression"""
        watch_frame = tk.LabelFrame(parent, text="Progressive Pattern Watch List", 
                                   font=('Arial', 12, 'bold'))
        watch_frame.pack(fill='both', expand=True, pady=(0, 5))
        
        # Watch list tree
        columns = ('Symbol', 'Stage', 'Declining Bars', 'Strength', 'Price', 'Last Update')
        self.watch_tree = ttk.Treeview(watch_frame, columns=columns, show='headings', height=12)
        
        # Configure columns
        column_widths = {'Symbol': 80, 'Stage': 120, 'Declining Bars': 100, 
                        'Strength': 80, 'Price': 80, 'Last Update': 100}
        
        for col in columns:
            self.watch_tree.heading(col, text=col)
            self.watch_tree.column(col, width=column_widths.get(col, 100), anchor='center')
        
        # Scrollbar
        watch_scrollbar = ttk.Scrollbar(watch_frame, orient='vertical', command=self.watch_tree.yview)
        self.watch_tree.configure(yscrollcommand=watch_scrollbar.set)
        
        self.watch_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        watch_scrollbar.pack(side='right', fill='y')
    
    def create_statistics_panel(self, parent):
        """Create statistics panel"""
        stats_frame = tk.LabelFrame(parent, text="Pattern Statistics", 
                                   font=('Arial', 12, 'bold'))
        stats_frame.pack(fill='x', pady=(5, 0))
        
        # Statistics grid
        stats_grid = tk.Frame(stats_frame)
        stats_grid.pack(fill='x', padx=10, pady=5)
        
        # Row 1
        tk.Label(stats_grid, text="Watch List Size:", font=('Arial', 10)).grid(row=0, column=0, sticky='w')
        self.watch_list_size_label = tk.Label(stats_grid, text="0", font=('Arial', 10, 'bold'))
        self.watch_list_size_label.grid(row=0, column=1, sticky='w', padx=(10, 20))
        
        tk.Label(stats_grid, text="Entry Signals Today:", font=('Arial', 10)).grid(row=0, column=2, sticky='w')
        self.entry_signals_label = tk.Label(stats_grid, text="0", font=('Arial', 10, 'bold'), fg='green')
        self.entry_signals_label.grid(row=0, column=3, sticky='w', padx=(10, 0))
        
        # Row 2
        tk.Label(stats_grid, text="Patterns Completed:", font=('Arial', 10)).grid(row=1, column=0, sticky='w')
        self.completed_patterns_label = tk.Label(stats_grid, text="0", font=('Arial', 10, 'bold'))
        self.completed_patterns_label.grid(row=1, column=1, sticky='w', padx=(10, 20))
        
        tk.Label(stats_grid, text="Patterns Failed:", font=('Arial', 10)).grid(row=1, column=2, sticky='w')
        self.failed_patterns_label = tk.Label(stats_grid, text="0", font=('Arial', 10, 'bold'))
        self.failed_patterns_label.grid(row=1, column=3, sticky='w', padx=(10, 0))
        
        # Stage breakdown
        stage_frame = tk.Frame(stats_frame)
        stage_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(stage_frame, text="By Stage:", font=('Arial', 10, 'bold')).pack(anchor='w')
        self.stage_labels = {}
        
        for i, stage in enumerate(['Stage 1', 'Stage 2', 'Stage 3', 'Stage 4', 'Complete']):
            label = tk.Label(stage_frame, text=f"{stage}: 0", font=('Arial', 9))
            label.pack(anchor='w')
            self.stage_labels[stage] = label
    
    def create_entry_signals_panel(self, parent):
        """Create entry signals panel"""
        signals_frame = tk.LabelFrame(parent, text="🎯 Entry Signals & Trading Opportunities", 
                                     font=('Arial', 12, 'bold'))
        signals_frame.pack(fill='both', expand=True, pady=(0, 5))
        
        # Entry signals list
        self.signals_listbox = tk.Listbox(signals_frame, font=('Consolas', 10))
        signals_scrollbar = ttk.Scrollbar(signals_frame, orient='vertical', command=self.signals_listbox.yview)
        self.signals_listbox.configure(yscrollcommand=signals_scrollbar.set)
        
        self.signals_listbox.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        signals_scrollbar.pack(side='right', fill='y')
        
        # Add header
        self.signals_listbox.insert(tk.END, "🎯 ENTRY SIGNALS - Ready to Trade")
        self.signals_listbox.insert(tk.END, "=" * 50)
    
    def create_log_panel(self, parent):
        """Create system log panel"""
        log_frame = tk.LabelFrame(parent, text="System Log", 
                                 font=('Arial', 12, 'bold'))
        log_frame.pack(fill='x', pady=(5, 0))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, 
                                                 font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Initial log messages
        self.log_message("Continuous scanner initialized")
        self.log_message(f"Daily target: ${TRADING_CONFIG.daily_profit_target}")
        self.log_message(f"Scan interval: 2 minutes during market hours")
    
    def log_message(self, message: str):
        """Add message to log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # Keep log manageable
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.log_text.delete("1.0", "20.0")
    
    def initialize_scanner(self):
        """Initialize the continuous scanner"""
        def init_task():
            try:
                self.message_queue.put(("log", "Initializing continuous scanner..."))
                self.scanner = ContinuousScanner(scan_interval_minutes=2, max_workers=6)
                self.message_queue.put(("log", f"Scanner ready - {len(self.scanner.universe)} stocks loaded"))
                self.message_queue.put(("status", "Ready"))
                
            except Exception as e:
                self.message_queue.put(("error", f"Scanner initialization failed: {e}"))
        
        threading.Thread(target=init_task, daemon=True).start()
    
    def start_scanning(self):
        """Start continuous scanning"""
        if self.is_scanning or not self.scanner:
            return
        
        self.is_scanning = True
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        
        def scanning_task():
            try:
                self.message_queue.put(("log", "🚀 Starting continuous real-time scanning..."))
                self.message_queue.put(("status", "Scanning"))
                
                # Start the scanner (this will run continuously)
                self.scanner.start_continuous_scanning(enable_auto_trading=False)
                
            except Exception as e:
                self.message_queue.put(("error", f"Scanning error: {e}"))
            finally:
                self.message_queue.put(("scanning_stopped", None))
        
        self.scanner_thread = threading.Thread(target=scanning_task, daemon=True)
        self.scanner_thread.start()
        
        # Start status update timer
        self.update_scanner_status()
    
    def stop_scanning(self):
        """Stop continuous scanning"""
        if self.scanner:
            self.scanner.stop_continuous_scanning()
        
        self.is_scanning = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.log_message("🛑 Scanning stopped")
    
    def update_scanner_status(self):
        """Update scanner status and data"""
        if self.is_scanning and self.scanner:
            try:
                status = self.scanner.get_current_status()
                
                # Update status labels
                self.scan_count_label.config(text=f"Scans: {status['scan_count']}")
                
                # Update market status
                if status['is_market_hours']:
                    self.market_status.config(text="Market: OPEN", fg='green')
                else:
                    self.market_status.config(text="Market: CLOSED", fg='red')
                
                # Update statistics
                self.watch_list_size_label.config(text=str(status['watch_list_size']))
                self.entry_signals_label.config(text=str(status['entry_signals_today']))
                self.completed_patterns_label.config(text=str(status['patterns_completed_today']))
                
                # Update watch list
                self.update_watch_list_display()
                
                # Update entry signals
                self.update_entry_signals_display(status.get('top_opportunities', []))
                
            except Exception as e:
                self.log_message(f"Error updating status: {e}")
        
        # Schedule next update
        if self.is_scanning:
            self.root.after(5000, self.update_scanner_status)  # Update every 5 seconds
    
    def update_watch_list_display(self):
        """Update watch list display"""
        if not self.scanner:
            return
        
        # Clear existing items
        for item in self.watch_tree.get_children():
            self.watch_tree.delete(item)
        
        # Get priority watch list
        priority_list = self.scanner.pattern_monitor.get_priority_watch_list(20)
        
        for progress in priority_list:
            # Format stage name
            stage_name = progress.stage.value.replace('_', ' ').title()
            
            # Format last update
            time_diff = (datetime.now() - progress.last_update).total_seconds() / 60
            last_update = f"{time_diff:.0f}m ago"
            
            # Determine row color based on stage
            if progress.stage == PatternStage.COMPLETE:
                tags = ('complete',)
            elif progress.stage == PatternStage.STAGE_4:
                tags = ('stage4',)
            elif progress.stage == PatternStage.STAGE_3:
                tags = ('stage3',)
            else:
                tags = ('normal',)
            
            self.watch_tree.insert('', 'end', values=(
                progress.symbol,
                stage_name,
                progress.consecutive_declining_bars,
                f"{progress.pattern_strength:.2f}",
                f"${progress.current_price:.2f}",
                last_update
            ), tags=tags)
        
        # Configure tag colors
        self.watch_tree.tag_configure('complete', background='#4CAF50', foreground='white')
        self.watch_tree.tag_configure('stage4', background='#FFC107', foreground='black')
        self.watch_tree.tag_configure('stage3', background='#FF9800', foreground='white')
        self.watch_tree.tag_configure('normal', background='white', foreground='black')
    
    def update_entry_signals_display(self, opportunities):
        """Update entry signals display"""
        # Clear old signals (keep header)
        self.signals_listbox.delete(2, tk.END)
        
        if opportunities:
            for opp in opportunities:
                if opp['stage'] == 'pattern_complete':
                    signal_text = f"🎯 {opp['symbol']} - READY TO TRADE (Strength: {opp['strength']:.2f})"
                    self.signals_listbox.insert(tk.END, signal_text)
        else:
            self.signals_listbox.insert(tk.END, "No entry signals at this time")
            self.signals_listbox.insert(tk.END, "System is monitoring for opportunities...")
    
    def process_messages(self):
        """Process messages from background threads"""
        try:
            while True:
                message_type, data = self.message_queue.get_nowait()
                
                if message_type == "log":
                    self.log_message(data)
                
                elif message_type == "status":
                    self.scan_status.config(text=f"Status: {data}")
                
                elif message_type == "scanning_stopped":
                    self.is_scanning = False
                    self.start_button.config(state='normal')
                    self.stop_button.config(state='disabled')
                    self.scan_status.config(text="Status: Stopped")
                
                elif message_type == "error":
                    self.log_message(f"❌ {data}")
                    messagebox.showerror("Error", data)
                
        except queue.Empty:
            pass
        
        # Schedule next check
        self.root.after(100, self.process_messages)
    
    def run(self):
        """Start the GUI application"""
        self.root.mainloop()


def main():
    """Main entry point"""
    try:
        app = ContinuousScannerGUI()
        app.run()
    except Exception as e:
        print(f"Error starting GUI: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
