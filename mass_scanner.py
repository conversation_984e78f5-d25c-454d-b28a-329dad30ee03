"""
High-Performance Mass Scanner for S&P 500 + $100B Market Cap Stocks
"""
import time
import logging
from datetime import datetime
from typing import List, Dict, Any, Tuple
from concurrent.futures import Thread<PERSON>ool<PERSON>xecutor, as_completed
import threading
from dataclasses import dataclass

from stock_universe import StockUniverse, create_scanning_batches
from automated_trading_system import AutomatedTradingSystem
from market_data_provider import MarketDataManager
from config import API_CONFIG, TRADING_CONFIG, SYSTEM_CONFIG


@dataclass
class ScanResult:
    """Result from scanning a single symbol"""
    symbol: str
    success: bool
    entry_signals: int
    declining_red_momentum: int
    momentum_reversals: int
    atr_confirmations: int
    pattern_strength: float
    current_price: float
    error_message: str = ""


class MassScanner:
    """High-performance scanner for large stock universes"""
    
    def __init__(self, max_workers: int = 10):
        self.max_workers = max_workers
        self.logger = logging.getLogger(__name__)
        self.scan_results: List[ScanResult] = []
        self.lock = threading.Lock()
        
        # Initialize market data manager
        self.data_manager = MarketDataManager(
            fmp_api_key=API_CONFIG.fmp_api_key,
            alpaca_api_key=API_CONFIG.alpaca_api_key,
            alpaca_secret=API_CONFIG.alpaca_secret_key
        )
        
        # Rate limiting
        self.last_request_time = {}
        self.min_request_interval = 0.1  # 100ms between requests per thread
    
    def scan_symbol(self, symbol: str) -> ScanResult:
        """Scan a single symbol for momentum reversal patterns"""
        try:
            # Rate limiting
            thread_id = threading.current_thread().ident
            current_time = time.time()
            
            if thread_id in self.last_request_time:
                time_since_last = current_time - self.last_request_time[thread_id]
                if time_since_last < self.min_request_interval:
                    time.sleep(self.min_request_interval - time_since_last)
            
            self.last_request_time[thread_id] = time.time()
            
            # Get market data
            market_data = self.data_manager.get_market_data(
                symbol=symbol,
                source="fmp",
                timeframe="1min",
                days=2  # Reduced for faster scanning
            )
            
            if not market_data or len(market_data) < 20:
                return ScanResult(
                    symbol=symbol,
                    success=False,
                    entry_signals=0,
                    declining_red_momentum=0,
                    momentum_reversals=0,
                    atr_confirmations=0,
                    pattern_strength=0.0,
                    current_price=0.0,
                    error_message="Insufficient data"
                )
            
            # Create a lightweight trading system for analysis
            from technical_indicators import TechnicalAnalyzer
            from pattern_detector import MomentumReversalDetector
            
            analyzer = TechnicalAnalyzer(
                momentum_period=10,  # Shorter for faster scanning
                atr_period=10,
                atr_multiplier=2.0
            )
            
            detector = MomentumReversalDetector(min_declining_bars=4)
            
            # Analyze recent data
            entry_signals = 0
            declining_red_momentum = 0
            momentum_reversals = 0
            atr_confirmations = 0
            
            # Process last 50 bars for efficiency
            recent_data = market_data[-50:] if len(market_data) > 50 else market_data
            
            for data_point in recent_data:
                technical_data = analyzer.analyze(data_point)
                pattern_signal = detector.detect_pattern(technical_data)
                
                if pattern_signal.entry_signal:
                    entry_signals += 1
                if pattern_signal.declining_red_momentum:
                    declining_red_momentum += 1
                if pattern_signal.momentum_reversal:
                    momentum_reversals += 1
                if pattern_signal.atr_confirmation:
                    atr_confirmations += 1
            
            # Get pattern strength
            pattern_strength = detector.get_pattern_strength()
            current_price = market_data[-1].close
            
            return ScanResult(
                symbol=symbol,
                success=True,
                entry_signals=entry_signals,
                declining_red_momentum=declining_red_momentum,
                momentum_reversals=momentum_reversals,
                atr_confirmations=atr_confirmations,
                pattern_strength=pattern_strength,
                current_price=current_price
            )
            
        except Exception as e:
            return ScanResult(
                symbol=symbol,
                success=False,
                entry_signals=0,
                declining_red_momentum=0,
                momentum_reversals=0,
                atr_confirmations=0,
                pattern_strength=0.0,
                current_price=0.0,
                error_message=str(e)
            )
    
    def scan_batch(self, symbols: List[str]) -> List[ScanResult]:
        """Scan a batch of symbols concurrently"""
        results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all scanning tasks
            future_to_symbol = {
                executor.submit(self.scan_symbol, symbol): symbol 
                for symbol in symbols
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    result = future.result(timeout=30)  # 30 second timeout
                    results.append(result)
                    
                    if result.success and result.entry_signals > 0:
                        self.logger.info(f"🎯 {symbol}: {result.entry_signals} entry signals!")
                    
                except Exception as e:
                    self.logger.error(f"Error scanning {symbol}: {e}")
                    results.append(ScanResult(
                        symbol=symbol,
                        success=False,
                        entry_signals=0,
                        declining_red_momentum=0,
                        momentum_reversals=0,
                        atr_confirmations=0,
                        pattern_strength=0.0,
                        current_price=0.0,
                        error_message=str(e)
                    ))
        
        return results
    
    def scan_universe(self, universe: List[str], batch_size: int = 50) -> List[ScanResult]:
        """Scan entire universe of stocks"""
        self.logger.info(f"Starting mass scan of {len(universe)} stocks...")
        start_time = time.time()
        
        # Create batches
        batches = create_scanning_batches(universe, batch_size)
        self.logger.info(f"Created {len(batches)} batches of {batch_size} stocks each")
        
        all_results = []
        
        for i, batch in enumerate(batches, 1):
            self.logger.info(f"Scanning batch {i}/{len(batches)} ({len(batch)} stocks)...")
            batch_start = time.time()
            
            batch_results = self.scan_batch(batch)
            all_results.extend(batch_results)
            
            batch_time = time.time() - batch_start
            self.logger.info(f"Batch {i} completed in {batch_time:.1f}s")
            
            # Show progress
            successful_scans = sum(1 for r in batch_results if r.success)
            entry_signals_found = sum(1 for r in batch_results if r.entry_signals > 0)
            
            self.logger.info(f"  Successful scans: {successful_scans}/{len(batch)}")
            if entry_signals_found > 0:
                self.logger.info(f"  🎯 Entry signals found: {entry_signals_found} stocks")
            
            # Brief pause between batches to be respectful to APIs
            if i < len(batches):
                time.sleep(1)
        
        total_time = time.time() - start_time
        self.logger.info(f"Mass scan completed in {total_time:.1f}s")
        
        self.scan_results = all_results
        return all_results
    
    def get_top_opportunities(self, results: List[ScanResult], top_n: int = 20) -> List[ScanResult]:
        """Get top trading opportunities from scan results"""
        # Filter successful scans with signals
        opportunities = [r for r in results if r.success and r.entry_signals > 0]
        
        # Sort by entry signals, then pattern strength
        opportunities.sort(
            key=lambda x: (x.entry_signals, x.pattern_strength, x.momentum_reversals),
            reverse=True
        )
        
        return opportunities[:top_n]
    
    def generate_scan_report(self, results: List[ScanResult]) -> Dict[str, Any]:
        """Generate comprehensive scan report"""
        successful_scans = [r for r in results if r.success]
        failed_scans = [r for r in results if not r.success]
        
        # Find opportunities
        entry_signals = [r for r in successful_scans if r.entry_signals > 0]
        declining_momentum = [r for r in successful_scans if r.declining_red_momentum > 0]
        momentum_reversals = [r for r in successful_scans if r.momentum_reversals > 0]
        atr_confirmations = [r for r in successful_scans if r.atr_confirmations > 0]
        
        # Top opportunities
        top_opportunities = self.get_top_opportunities(results, 10)
        
        return {
            "scan_summary": {
                "total_stocks": len(results),
                "successful_scans": len(successful_scans),
                "failed_scans": len(failed_scans),
                "success_rate": len(successful_scans) / len(results) * 100 if results else 0
            },
            "pattern_detection": {
                "stocks_with_entry_signals": len(entry_signals),
                "stocks_with_declining_momentum": len(declining_momentum),
                "stocks_with_momentum_reversals": len(momentum_reversals),
                "stocks_with_atr_confirmation": len(atr_confirmations)
            },
            "top_opportunities": [
                {
                    "symbol": r.symbol,
                    "entry_signals": r.entry_signals,
                    "pattern_strength": r.pattern_strength,
                    "current_price": r.current_price,
                    "momentum_reversals": r.momentum_reversals,
                    "atr_confirmations": r.atr_confirmations
                }
                for r in top_opportunities
            ],
            "error_summary": {
                "common_errors": self._get_common_errors(failed_scans)
            }
        }
    
    def _get_common_errors(self, failed_scans: List[ScanResult]) -> Dict[str, int]:
        """Get summary of common errors"""
        error_counts = {}
        for scan in failed_scans:
            error = scan.error_message
            if error:
                error_counts[error] = error_counts.get(error, 0) + 1
        return error_counts


def main():
    """Run mass scanner on S&P 500 + $100B market cap stocks"""
    print("=" * 80)
    print("MASS SCANNER - S&P 500 + $100B MARKET CAP STOCKS")
    print("=" * 80)
    
    # Build stock universe
    universe_manager = StockUniverse()
    universe = universe_manager.build_universe()
    
    print(f"Built universe of {len(universe)} stocks")
    print(f"Starting momentum reversal pattern scan...")
    
    # Initialize scanner
    scanner = MassScanner(max_workers=8)  # 8 concurrent threads
    
    # Run the scan
    results = scanner.scan_universe(universe, batch_size=25)  # Smaller batches for stability
    
    # Generate report
    report = scanner.generate_scan_report(results)
    
    # Display results
    print(f"\n" + "=" * 80)
    print("SCAN RESULTS")
    print("=" * 80)
    
    summary = report["scan_summary"]
    patterns = report["pattern_detection"]
    
    print(f"Scan Summary:")
    print(f"  Total Stocks Scanned: {summary['total_stocks']}")
    print(f"  Successful Scans: {summary['successful_scans']}")
    print(f"  Success Rate: {summary['success_rate']:.1f}%")
    
    print(f"\nPattern Detection:")
    print(f"  🎯 Stocks with Entry Signals: {patterns['stocks_with_entry_signals']}")
    print(f"  📉 Stocks with Declining Momentum: {patterns['stocks_with_declining_momentum']}")
    print(f"  🔄 Stocks with Momentum Reversals: {patterns['stocks_with_momentum_reversals']}")
    print(f"  ✅ Stocks with ATR Confirmation: {patterns['stocks_with_atr_confirmation']}")
    
    # Show top opportunities
    opportunities = report["top_opportunities"]
    if opportunities:
        print(f"\n🏆 TOP TRADING OPPORTUNITIES:")
        print("-" * 60)
        print(f"{'Symbol':<8} {'Signals':<8} {'Strength':<10} {'Price':<10} {'Reversals':<10}")
        print("-" * 60)
        
        for opp in opportunities:
            print(f"{opp['symbol']:<8} {opp['entry_signals']:<8} "
                  f"{opp['pattern_strength']:<10.2f} ${opp['current_price']:<9.2f} "
                  f"{opp['momentum_reversals']:<10}")
    else:
        print(f"\n📊 No entry signals found in current market conditions")
        print(f"This is normal - the system waits for high-probability setups")
    
    print(f"\n" + "=" * 80)
    print("MASS SCAN COMPLETE!")
    print("=" * 80)


if __name__ == "__main__":
    main()
